'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { PageTitle } from '@/components/page-title';

export default function Home() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Get user role and redirect to appropriate dashboard
    const redirectToDashboard = async () => {
      try {
        const response = await fetch('/api/auth/user');
        const authData = await response.json();

        if (authData.success && authData.user) {
          const userData = authData.user;

          if (userData.isPlatformAdmin) {
            router.push('/admin');
          } else if (userData.isOrgOwner || userData.isOrgAdmin || userData.role === 'org_member') {
            // Get the first organization slug for routing
            const firstEmployment = userData.employmentRelationships?.[0];
            const orgSlug = firstEmployment?.organization_slug || 'default';
            router.push(`/org/${orgSlug}`);
          } else if (userData.role === 'individual') {
            router.push('/individual');
          } else {
            router.push('/dashboard');
          }
        } else {
          router.push('/login');
        }
      } catch (err) {
        console.error('Redirect error:', err);
        router.push('/login');
      } finally {
        setLoading(false);
      }
    };

    redirectToDashboard();
  }, [router]);

  if (loading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <PageTitle title="Luna Skills Platform" />
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"></div>
          <h2 className="mt-4 text-xl font-medium text-gray-900">Welcome to Luna Platform</h2>
          <p className="mt-2 text-gray-500">Redirecting you to your dashboard...</p>
        </div>
      </div>
    );
  }

  return null;
}
