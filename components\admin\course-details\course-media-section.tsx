"use client"

import React, { useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Save, Image, Video, FileText } from 'lucide-react'
import { toast } from "sonner"

interface Course {
  id: string
  name: string
  description: string
  slug: string
  level: string
  estimated_duration: number
  status: string
  price: number
  cover_image_url?: string
  preview_video_url?: string
  meta_description?: string
  tags: string[]
  learning_objectives: string[]
  target_audience?: string
  instructor_id?: string
  instructor_bio?: string
  enrollment_count: number
  completion_rate: number
  average_rating: number
  course_complexity: string
  certification_available: boolean
  is_standalone: boolean
  required_software: string[]
  hardware_requirements?: string
  language: string
  accessibility_features: string[]
  prerequisite_courses: string[]
  created_at: string
  updated_at: string
}

interface CourseMediaSectionProps {
  course: Course
  onUpdate: () => void
}

export function CourseMediaSection({ course, onUpdate }: CourseMediaSectionProps) {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    cover_image_url: course.cover_image_url || '',
    preview_video_url: course.preview_video_url || '',
    meta_description: course.meta_description || ''
  })

  const handleSave = async () => {
    try {
      setLoading(true)

      const payload = {
        cover_image_url: formData.cover_image_url?.trim() || null,
        preview_video_url: formData.preview_video_url?.trim() || null,
        meta_description: formData.meta_description?.trim() || null
      }

      const response = await fetch(`/api/admin/courses/${course.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to update course media')
      }

      toast.success('Course media updated successfully')
      onUpdate()
      
    } catch (error: any) {
      console.error('Error updating course media:', error)
      toast.error(error.message || 'Failed to update course media')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-end">
        <Button
          size="sm"
          onClick={handleSave}
          disabled={loading}
        >
          <Save className="h-4 w-4 mr-2" />
          {loading ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>
        <div className="space-y-4">
          {/* Cover Image */}
          <div className="space-y-2">
            <Label htmlFor="cover_image_url" className="flex items-center gap-2">
              <Image className="h-4 w-4" />
              Cover Image URL
            </Label>
            <Input
              id="cover_image_url"
              value={formData.cover_image_url}
              onChange={(e) => setFormData({ ...formData, cover_image_url: e.target.value })}
              placeholder="https://example.com/course-cover.jpg"
            />
            <p className="text-sm text-muted-foreground">
              Recommended size: 1200x630px. Supports JPG, PNG, WebP formats.
            </p>
            {formData.cover_image_url && (
              <div className="mt-2">
                <img
                  src={formData.cover_image_url}
                  alt="Cover preview"
                  className="w-full max-w-sm h-32 object-cover rounded-lg border"
                  onError={(e) => {
                    e.currentTarget.style.display = 'none'
                  }}
                />
              </div>
            )}
          </div>

          {/* Preview Video */}
          <div className="space-y-2">
            <Label htmlFor="preview_video_url" className="flex items-center gap-2">
              <Video className="h-4 w-4" />
              Preview Video URL
            </Label>
            <Input
              id="preview_video_url"
              value={formData.preview_video_url}
              onChange={(e) => setFormData({ ...formData, preview_video_url: e.target.value })}
              placeholder="https://youtube.com/watch?v=... or https://vimeo.com/..."
            />
            <p className="text-sm text-muted-foreground">
              YouTube, Vimeo, or direct video file URLs supported. Keep under 5 minutes for best engagement.
            </p>
          </div>

          {/* Meta Description */}
          <div className="space-y-2">
            <Label htmlFor="meta_description" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              SEO Meta Description
            </Label>
            <Textarea
              id="meta_description"
              value={formData.meta_description}
              onChange={(e) => setFormData({ ...formData, meta_description: e.target.value })}
              placeholder="Brief description for search engines and social media sharing..."
              rows={3}
              maxLength={160}
            />
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>Used for SEO and social media previews</span>
              <span>{formData.meta_description.length}/160</span>
            </div>
          </div>
        </div>
    </div>
  )
}
