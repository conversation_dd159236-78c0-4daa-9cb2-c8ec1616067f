"use client"

import React, { useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Save, Tag, BookOpen, AlertCircle } from 'lucide-react'
import { toast } from "sonner"

interface Course {
  id: string
  name: string
  description: string
  slug: string
  level: string
  estimated_duration: number
  status: string
  price: number
  cover_image_url?: string
  preview_video_url?: string
  meta_description?: string
  tags: string[]
  learning_objectives: string[]
  target_audience?: string
  instructor_id?: string
  instructor_bio?: string
  enrollment_count: number
  completion_rate: number
  average_rating: number
  course_complexity: string
  certification_available: boolean
  is_standalone: boolean
  required_software: string[]
  hardware_requirements?: string
  language: string
  accessibility_features: string[]
  prerequisite_courses: string[]
  created_at: string
  updated_at: string
}

interface CourseTagsSectionProps {
  course: Course
  onUpdate: () => void
}

export function CourseTagsSection({ course, onUpdate }: CourseTagsSectionProps) {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    tags: course.tags.join(', '),
    prerequisite_courses: course.prerequisite_courses.join(', ')
  })

  const handleSave = async () => {
    try {
      setLoading(true)

      const payload = {
        tags: formData.tags 
          ? formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0)
          : [],
        prerequisite_courses: formData.prerequisite_courses 
          ? formData.prerequisite_courses.split(',').map(course => course.trim()).filter(course => course.length > 0)
          : []
      }

      const response = await fetch(`/api/admin/courses/${course.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to update tags and categories')
      }

      toast.success('Tags and categories updated successfully')
      onUpdate()
      
    } catch (error: any) {
      console.error('Error updating tags and categories:', error)
      toast.error(error.message || 'Failed to update tags and categories')
    } finally {
      setLoading(false)
    }
  }

  // Parse tags for preview
  const tagList = formData.tags 
    ? formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0)
    : []

  const prerequisiteList = formData.prerequisite_courses 
    ? formData.prerequisite_courses.split(',').map(course => course.trim()).filter(course => course.length > 0)
    : []

  return (
    <div className="space-y-6">
      <div className="flex justify-end">
        <Button
          size="sm"
          onClick={handleSave}
          disabled={loading}
        >
          <Save className="h-4 w-4 mr-2" />
          {loading ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>
        <div className="space-y-4">
          {/* Tags */}
          <div className="space-y-2">
            <Label htmlFor="tags" className="flex items-center gap-2">
              <Tag className="h-4 w-4" />
              Course Tags
            </Label>
            <Textarea
              id="tags"
              value={formData.tags}
              onChange={(e) => setFormData({ ...formData, tags: e.target.value })}
              placeholder="Enter tags separated by commas:&#10;JavaScript, Web Development, Frontend, React, Programming, Beginner Friendly"
              rows={3}
            />
            <p className="text-sm text-muted-foreground">
              Add relevant tags to help students discover this course. Separate multiple tags with commas.
            </p>
            
            {/* Tag Preview */}
            {tagList.length > 0 && (
              <div className="space-y-2">
                <Label className="text-sm font-medium">Tag Preview:</Label>
                <div className="flex flex-wrap gap-2">
                  {tagList.map((tag, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Prerequisites */}
          <div className="space-y-2">
            <Label htmlFor="prerequisite_courses" className="flex items-center gap-2">
              <BookOpen className="h-4 w-4" />
              Prerequisite Courses
            </Label>
            <Textarea
              id="prerequisite_courses"
              value={formData.prerequisite_courses}
              onChange={(e) => setFormData({ ...formData, prerequisite_courses: e.target.value })}
              placeholder="Enter prerequisite course names or IDs separated by commas:&#10;HTML Fundamentals, CSS Basics, Introduction to Programming"
              rows={3}
            />
            <p className="text-sm text-muted-foreground">
              List courses students should complete before taking this course. Leave empty if no prerequisites.
            </p>

            {/* Prerequisites Preview */}
            {prerequisiteList.length > 0 && (
              <div className="space-y-2">
                <Label className="text-sm font-medium">Prerequisites Preview:</Label>
                <div className="space-y-1">
                  {prerequisiteList.map((prereq, index) => (
                    <div key={index} className="flex items-center gap-2 text-sm">
                      <AlertCircle className="h-3 w-3 text-amber-500" />
                      <span>{prereq}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Course Categories Info */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start gap-2">
              <Tag className="h-4 w-4 text-blue-600 mt-0.5" />
              <div className="text-sm">
                <p className="font-medium text-blue-900 mb-1">Tagging Best Practices</p>
                <ul className="text-blue-700 space-y-1 text-xs">
                  <li>• Use specific, relevant keywords that students might search for</li>
                  <li>• Include skill level indicators (beginner, intermediate, advanced)</li>
                  <li>• Add technology/tool names and industry categories</li>
                  <li>• Keep tags concise and avoid duplicating course title words</li>
                  <li>• Use 5-10 tags for optimal discoverability</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
    </div>
  )
}
