import { NextRequest, NextResponse } from 'next/server'
import { createAdminClient } from '@/lib/supabase'
import { requireAuth, createAuthErrorResponse } from '@/lib/auth-utils'

// Quiz scoring service
class QuizScoringService {
  static calculateScore(questions: any[], answers: Record<string, any>): {
    score: number
    totalPoints: number
    earnedPoints: number
    questionResults: Array<{
      questionId: string
      correct: boolean
      points: number
      earnedPoints: number
    }>
  } {
    let totalPoints = 0
    let earnedPoints = 0
    const questionResults = []

    for (const question of questions) {
      totalPoints += question.points
      const userAnswer = answers[question.id]
      let isCorrect = false
      let pointsEarned = 0

      if (userAnswer !== undefined && userAnswer !== null && userAnswer !== '') {
        switch (question.question_type) {
          case 'multiple_choice':
            const correctOption = question.answer_options?.find((opt: any) => opt.is_correct)
            if (correctOption && userAnswer === correctOption.id) {
              isCorrect = true
              pointsEarned = question.points
            }
            break

          case 'true_false':
            if (userAnswer.toString().toLowerCase() === question.correct_answer.toLowerCase()) {
              isCorrect = true
              pointsEarned = question.points
            }
            break

          case 'short_answer':
            // Simple string comparison (in production, this would be more sophisticated)
            const userAnswerNormalized = userAnswer.toString().toLowerCase().trim()
            const correctAnswerNormalized = question.correct_answer.toLowerCase().trim()
            
            if (userAnswerNormalized === correctAnswerNormalized) {
              isCorrect = true
              pointsEarned = question.points
            } else if (userAnswerNormalized.includes(correctAnswerNormalized) || 
                      correctAnswerNormalized.includes(userAnswerNormalized)) {
              // Partial credit for partial matches
              isCorrect = true
              pointsEarned = Math.round(question.points * 0.7)
            }
            break

          case 'essay':
            // Essay questions would typically require manual grading
            // For now, give partial credit if answer is substantial
            if (userAnswer.toString().trim().length > 50) {
              isCorrect = true
              pointsEarned = Math.round(question.points * 0.8) // 80% for attempt
            }
            break

          case 'code_challenge':
            // Code challenges would require code execution/validation
            // For now, give credit if code is provided
            if (userAnswer.toString().trim().length > 20) {
              isCorrect = true
              pointsEarned = Math.round(question.points * 0.7) // 70% for attempt
            }
            break
        }
      }

      earnedPoints += pointsEarned
      questionResults.push({
        questionId: question.id,
        correct: isCorrect,
        points: question.points,
        earnedPoints: pointsEarned
      })
    }

    const score = totalPoints > 0 ? Math.round((earnedPoints / totalPoints) * 100) : 0

    return {
      score,
      totalPoints,
      earnedPoints,
      questionResults
    }
  }
}

export async function POST(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const authResult = await requireAuth()
    if (!authResult.user) {
      return createAuthErrorResponse(authResult)
    }

    const lessonId = params.id
    const body = await req.json()
    
    const {
      generation_id,
      answers = {},
      time_spent = 0
    } = body

    if (!generation_id) {
      return NextResponse.json(
        { error: 'Generation ID is required' },
        { status: 400 }
      )
    }

    const supabase = createAdminClient()

    // Verify the generation belongs to the user and lesson
    const { data: generation, error: generationError } = await supabase
      .from('lesson_quiz_generations')
      .select('*')
      .eq('id', generation_id)
      .eq('user_id', authResult.user.id)
      .eq('lesson_id', lessonId)
      .single()

    if (generationError || !generation) {
      return NextResponse.json(
        { error: 'Invalid quiz generation' },
        { status: 404 }
      )
    }

    // Get the quiz questions
    const { data: questions, error: questionsError } = await supabase
      .from('lesson_quiz_user_questions')
      .select('*')
      .eq('generation_id', generation_id)
      .order('question_number')

    if (questionsError || !questions) {
      return NextResponse.json(
        { error: 'Failed to fetch quiz questions' },
        { status: 500 }
      )
    }

    // Get lesson info for passing score
    const { data: lesson, error: lessonError } = await supabase
      .from('course_lessons')
      .select('passing_score, attempts_allowed')
      .eq('id', lessonId)
      .single()

    if (lessonError || !lesson) {
      return NextResponse.json(
        { error: 'Lesson not found' },
        { status: 404 }
      )
    }

    // Check if user has already completed this quiz
    const { data: existingAttempt } = await supabase
      .from('lesson_quiz_attempts')
      .select('attempt_number')
      .eq('generation_id', generation_id)
      .eq('user_id', authResult.user.id)
      .eq('lesson_id', lessonId)
      .order('attempt_number', { ascending: false })
      .limit(1)
      .single()

    const attemptNumber = existingAttempt ? existingAttempt.attempt_number + 1 : 1

    // Check if user has exceeded attempt limit
    if (attemptNumber > (lesson.attempts_allowed || 3)) {
      return NextResponse.json(
        { error: 'Maximum attempts exceeded' },
        { status: 403 }
      )
    }

    // Calculate score
    const scoringResult = QuizScoringService.calculateScore(questions, answers)
    const passingScore = lesson.passing_score || 70
    const passed = scoringResult.score >= passingScore

    // Create quiz attempt record
    const { data: attempt, error: attemptError } = await supabase
      .from('lesson_quiz_attempts')
      .insert({
        generation_id: generation_id,
        user_id: authResult.user.id,
        lesson_id: lessonId,
        attempt_number: attemptNumber,
        completed_at: new Date().toISOString(),
        status: 'completed',
        score: scoringResult.score,
        passed: passed,
        time_spent: time_spent,
        answers_data: {
          answers: answers,
          question_results: scoringResult.questionResults,
          scoring_metadata: {
            total_points: scoringResult.totalPoints,
            earned_points: scoringResult.earnedPoints,
            passing_score: passingScore
          }
        }
      })
      .select()
      .single()

    if (attemptError) {
      console.error('Error creating quiz attempt:', attemptError)
      return NextResponse.json(
        { error: 'Failed to save quiz attempt' },
        { status: 500 }
      )
    }

    // Update generation status to completed
    await supabase
      .from('lesson_quiz_generations')
      .update({ status: 'completed' })
      .eq('id', generation_id)

    return NextResponse.json({
      success: true,
      message: 'Quiz submitted successfully',
      data: {
        attempt_id: attempt.id,
        score: scoringResult.score,
        passed: passed,
        total_points: scoringResult.totalPoints,
        earned_points: scoringResult.earnedPoints,
        passing_score: passingScore,
        attempt_number: attemptNumber,
        max_attempts: lesson.attempts_allowed || 3,
        time_spent: time_spent,
        question_results: scoringResult.questionResults,
        can_retake: !passed && attemptNumber < (lesson.attempts_allowed || 3)
      }
    })

  } catch (error: any) {
    console.error('Quiz submission error:', error)
    return NextResponse.json(
      { error: error.message || 'Failed to submit quiz' },
      { status: 500 }
    )
  }
}

// GET endpoint to retrieve quiz attempt results
export async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const authResult = await requireAuth()
    if (!authResult.user) {
      return createAuthErrorResponse(authResult)
    }

    const lessonId = params.id
    const supabase = createAdminClient()

    // Get user's quiz attempts for this lesson
    const { data: attempts, error: attemptsError } = await supabase
      .from('lesson_quiz_attempts')
      .select(`
        *,
        lesson_quiz_generations (
          generation_timestamp,
          question_count,
          ai_model_used
        )
      `)
      .eq('lesson_id', lessonId)
      .eq('user_id', authResult.user.id)
      .order('attempt_number', { ascending: false })

    if (attemptsError) {
      return NextResponse.json(
        { error: 'Failed to fetch quiz attempts' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        attempts: attempts || [],
        total_attempts: attempts?.length || 0,
        best_score: attempts?.length > 0 ? Math.max(...attempts.map(a => a.score || 0)) : 0,
        latest_attempt: attempts?.[0] || null
      }
    })

  } catch (error: any) {
    console.error('Quiz results fetch error:', error)
    return NextResponse.json(
      { error: error.message || 'Failed to fetch quiz results' },
      { status: 500 }
    )
  }
}
