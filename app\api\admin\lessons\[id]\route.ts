import { NextRequest, NextResponse } from 'next/server'
import { requirePlatformAdmin } from '@/lib/auth/middleware'
import { createAdminClient } from '@/lib/supabase-admin'

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const authResult = await requirePlatformAdmin()
    if (!authResult.user) {
      return NextResponse.json(
        { error: authResult.error || 'Authentication required' },
        { status: authResult.status || 401 }
      )
    }

    const lessonId = params.id
    const body = await request.json()

    const {
      name,
      description,
      lesson_type,
      content_url,
      estimated_duration,
      is_mandatory = true,
      has_assessment = false,
      assessment_type,
      weight_in_module = 0,
      status = 'draft',
      sequence_order,
      // Video-specific fields
      video_duration,
      transcript,
      // Text-specific fields
      reading_time,
      // Assessment-specific fields
      passing_score,
      attempts_allowed,
      time_limit,
      // Assignment-specific fields
      submission_format,
      max_file_size
    } = body

    const supabase = createAdminClient()

    // Update the lesson
    const { data: lesson, error: lessonError } = await supabase
      .from('course_lessons')
      .update({
        name: name?.trim(),
        description: description?.trim(),
        lesson_type,
        content_url: content_url?.trim() || null,
        estimated_duration,
        is_mandatory,
        has_assessment,
        assessment_type: has_assessment ? assessment_type : null,
        weight_in_module,
        status,
        sequence_order,
        video_duration,
        transcript: transcript?.trim() || null,
        reading_time,
        passing_score,
        attempts_allowed,
        time_limit,
        submission_format: submission_format || [],
        max_file_size,
        updated_at: new Date().toISOString()
      })
      .eq('id', lessonId)
      .select()
      .single()

    if (lessonError) {
      console.error('Error updating lesson:', lessonError)
      return NextResponse.json(
        { error: 'Failed to update lesson' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      lesson,
      message: 'Lesson updated successfully'
    })

  } catch (error) {
    console.error('Error in PUT /api/admin/lessons/[id]:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const authResult = await requirePlatformAdmin()
    if (!authResult.user) {
      return NextResponse.json(
        { error: authResult.error || 'Authentication required' },
        { status: authResult.status || 401 }
      )
    }

    const lessonId = params.id
    const supabase = createAdminClient()

    // Delete the lesson
    const { error: deleteError } = await supabase
      .from('course_lessons')
      .delete()
      .eq('id', lessonId)

    if (deleteError) {
      console.error('Error deleting lesson:', deleteError)
      return NextResponse.json(
        { error: 'Failed to delete lesson' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      message: 'Lesson deleted successfully'
    })

  } catch (error) {
    console.error('Error in DELETE /api/admin/lessons/[id]:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
