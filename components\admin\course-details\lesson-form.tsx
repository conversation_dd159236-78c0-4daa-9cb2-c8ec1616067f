"use client"

import { useState, useCallback } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Save, X } from "lucide-react"
import { toast } from "sonner"

interface LessonFormProps {
  moduleId: string
  onCancel: () => void
  onSuccess: () => void
}

export function LessonForm({ moduleId, onCancel, onSuccess }: LessonFormProps) {
  const [loading, setLoading] = useState(false)
  const [newLesson, setNewLesson] = useState({
    name: '',
    description: '',
    lesson_type: 'video',
    content_url: '',
    estimated_duration: '',
    is_mandatory: true,
    has_assessment: false,
    assessment_type: '',
    weight_in_module: '',
    status: 'draft',
    // Content delivery options
    content_type: 'url',
    uploaded_file: null as File | null,
    // Video-specific fields
    video_duration: '',
    transcript: '',
    // Text-specific fields
    reading_time: '',
    // Presentation-specific fields
    slide_count: '',
    // Quiz-specific fields
    passing_score: '',
    attempts_allowed: '3',
    time_limit: '',
    quiz_instructions: '',
    quiz_generation_method: 'ai',
    quiz_question_count: '10',
    quiz_question_types: 'multiple_choice',
    quiz_learning_objectives: '',
    // Live lesson-specific fields
    zoom_meeting_id: '',
    zoom_meeting_url: '',
    scheduled_start: '',
    scheduled_end: '',
    waiting_room_enabled: true,
    max_participants: ''
  })

  const updateNewLesson = useCallback((field: string, value: any) => {
    setNewLesson(prev => ({
      ...prev,
      [field]: value
    }))
  }, [])

  const handleAddLesson = async () => {
    try {
      setLoading(true)

      // Handle file upload first if needed
      let uploadedFileUrl = null
      let uploadedFileName = null
      let uploadedFileSize = null
      let uploadedFileType = null

      if (newLesson.content_type === 'upload' && newLesson.uploaded_file) {
        const formData = new FormData()
        formData.append('file', newLesson.uploaded_file)
        formData.append('folder', 'course-media')

        const uploadResponse = await fetch('/api/course-media/upload', {
          method: 'POST',
          body: formData,
        })

        if (!uploadResponse.ok) {
          throw new Error('Failed to upload file')
        }

        const uploadResult = await uploadResponse.json()
        uploadedFileUrl = uploadResult.url
        uploadedFileName = newLesson.uploaded_file.name
        uploadedFileSize = newLesson.uploaded_file.size
        uploadedFileType = newLesson.uploaded_file.type
      }

      const payload = {
        name: newLesson.name.trim(),
        description: newLesson.description.trim(),
        lesson_type: newLesson.lesson_type,
        content_url: uploadedFileUrl || newLesson.content_url?.trim() || null,
        content_source: newLesson.content_type || 'url',
        content_file_url: uploadedFileUrl,
        content_file_name: uploadedFileName,
        content_file_size: uploadedFileSize,
        content_file_type: uploadedFileType,
        estimated_duration: newLesson.estimated_duration ? parseInt(newLesson.estimated_duration) : null,
        is_mandatory: newLesson.is_mandatory,
        has_assessment: newLesson.has_assessment,
        assessment_type: newLesson.has_assessment ? newLesson.assessment_type : null,
        weight_in_module: newLesson.weight_in_module ? parseFloat(newLesson.weight_in_module) : 0,
        status: newLesson.status,
        // Conditional fields based on lesson type
        video_duration: newLesson.lesson_type === 'video' ? (newLesson.video_duration ? parseInt(newLesson.video_duration) : null) : null,
        transcript: newLesson.lesson_type === 'video' ? newLesson.transcript?.trim() || null : null,
        reading_time: newLesson.lesson_type === 'text' ? (newLesson.reading_time ? parseInt(newLesson.reading_time) : null) : null,
        slide_count: newLesson.lesson_type === 'presentation' ? (newLesson.slide_count ? parseInt(newLesson.slide_count) : null) : null,
        passing_score: newLesson.lesson_type === 'quiz' ? (newLesson.passing_score ? parseInt(newLesson.passing_score) : null) : null,
        attempts_allowed: newLesson.lesson_type === 'quiz' ? (newLesson.attempts_allowed ? parseInt(newLesson.attempts_allowed) : 3) : null,
        time_limit: newLesson.lesson_type === 'quiz' ? (newLesson.time_limit ? parseInt(newLesson.time_limit) : null) : null,
        // Quiz-specific fields
        quiz_instructions: newLesson.lesson_type === 'quiz' ? newLesson.quiz_instructions?.trim() || null : null,
        quiz_generation_method: newLesson.lesson_type === 'quiz' ? newLesson.quiz_generation_method : null,
        quiz_question_count: newLesson.lesson_type === 'quiz' && newLesson.quiz_generation_method === 'ai' ? (newLesson.quiz_question_count ? parseInt(newLesson.quiz_question_count) : 10) : null,
        quiz_question_types: newLesson.lesson_type === 'quiz' && newLesson.quiz_generation_method === 'ai' ? newLesson.quiz_question_types : null,
        quiz_learning_objectives: newLesson.lesson_type === 'quiz' && newLesson.quiz_generation_method === 'ai' ? newLesson.quiz_learning_objectives?.trim() || null : null,
        // Live lesson fields
        scheduled_start: newLesson.lesson_type === 'live' ? newLesson.scheduled_start || null : null,
        scheduled_end: newLesson.lesson_type === 'live' ? newLesson.scheduled_end || null : null,
        waiting_room_enabled: newLesson.lesson_type === 'live' ? newLesson.waiting_room_enabled : null,
        max_participants: newLesson.lesson_type === 'live' ? (newLesson.max_participants ? parseInt(newLesson.max_participants) : null) : null,
        zoom_meeting_url: newLesson.lesson_type === 'live' ? newLesson.zoom_meeting_url || null : null
      }

      const response = await fetch(`/api/admin/modules/${moduleId}/lessons`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to create lesson')
      }

      toast.success('Lesson created successfully')
      onSuccess()
      
    } catch (error: any) {
      console.error('Error creating lesson:', error)
      toast.error(error.message || 'Failed to create lesson')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card className="mb-4">
      <CardContent className="p-4 space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label>Lesson Name *</Label>
            <Input
              value={newLesson.name}
              onChange={(e) => updateNewLesson('name', e.target.value)}
              placeholder="Enter lesson name"
            />
          </div>
          <div className="space-y-2">
            <Label>Lesson Type</Label>
            <Select value={newLesson.lesson_type || 'video'} onValueChange={(value) => updateNewLesson('lesson_type', value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="video">Video</SelectItem>
                <SelectItem value="text">Text/Reading</SelectItem>
                <SelectItem value="presentation">Presentation</SelectItem>
                <SelectItem value="quiz">Quiz</SelectItem>
                <SelectItem value="live">Live Lesson</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="space-y-2">
          <Label>Description</Label>
          <Textarea
            value={newLesson.description}
            onChange={(e) => updateNewLesson('description', e.target.value)}
            placeholder="Describe what this lesson covers"
            rows={2}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label>Duration (minutes)</Label>
            <Input
              type="number"
              value={newLesson.estimated_duration}
              onChange={(e) => updateNewLesson('estimated_duration', e.target.value)}
              placeholder="15"
            />
          </div>
          <div className="flex items-center space-x-2">
            <Switch
              checked={newLesson.is_mandatory}
              onCheckedChange={(checked) => updateNewLesson('is_mandatory', checked)}
            />
            <Label>Mandatory</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Switch
              checked={newLesson.has_assessment}
              onCheckedChange={(checked) => updateNewLesson('has_assessment', checked)}
            />
            <Label>Has Assessment</Label>
          </div>
        </div>

        {/* Conditional Fields Based on Lesson Type */}
        {newLesson.lesson_type === 'video' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-blue-50 rounded-lg">
            <div className="space-y-2">
              <Label>Content Type</Label>
              <Select
                value={newLesson.content_type || 'url'}
                onValueChange={(value) => updateNewLesson('content_type', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="url">External Link</SelectItem>
                  <SelectItem value="upload">Upload File</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>Video Duration (minutes)</Label>
              <Input
                type="number"
                value={newLesson.video_duration}
                onChange={(e) => updateNewLesson('video_duration', e.target.value)}
                placeholder="10"
              />
            </div>
            {newLesson.content_type === 'url' ? (
              <div className="space-y-2 md:col-span-2">
                <Label>Video URL</Label>
                <Input
                  value={newLesson.content_url}
                  onChange={(e) => updateNewLesson('content_url', e.target.value)}
                  placeholder="https://youtube.com/watch?v=... or https://vimeo.com/..."
                />
              </div>
            ) : (
              <div className="space-y-2 md:col-span-2">
                <Label>Upload Video</Label>
                <Input
                  type="file"
                  accept=".mp4,.mov,.avi,.wmv"
                  onChange={(e) => updateNewLesson('uploaded_file', e.target.files?.[0] || null)}
                />
                <p className="text-xs text-muted-foreground">Supported formats: MP4, MOV, AVI, WMV</p>
              </div>
            )}
            <div className="space-y-2 md:col-span-2">
              <Label>Transcript (optional)</Label>
              <Textarea
                value={newLesson.transcript || ''}
                onChange={(e) => updateNewLesson('transcript', e.target.value)}
                placeholder="Video transcript for accessibility..."
                rows={3}
              />
            </div>
          </div>
        )}

        {/* Text/Reading Lesson Fields */}
        {newLesson.lesson_type === 'text' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-green-50 rounded-lg">
            <div className="space-y-2">
              <Label>Content Type</Label>
              <Select
                value={newLesson.content_type || 'url'}
                onValueChange={(value) => updateNewLesson('content_type', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="url">External Link</SelectItem>
                  <SelectItem value="upload">Upload PDF</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>Reading Time (minutes)</Label>
              <Input
                type="number"
                value={newLesson.reading_time || ''}
                onChange={(e) => updateNewLesson('reading_time', e.target.value)}
                placeholder="15"
              />
            </div>
            {newLesson.content_type === 'url' ? (
              <div className="space-y-2 md:col-span-2">
                <Label>Document URL</Label>
                <Input
                  value={newLesson.content_url || ''}
                  onChange={(e) => updateNewLesson('content_url', e.target.value)}
                  placeholder="https://example.com/document.pdf"
                />
              </div>
            ) : (
              <div className="space-y-2 md:col-span-2">
                <Label>Upload PDF Document</Label>
                <Input
                  type="file"
                  accept=".pdf,.doc,.docx"
                  onChange={(e) => updateNewLesson('uploaded_file', e.target.files?.[0] || null)}
                />
                <p className="text-xs text-muted-foreground">Supported formats: PDF, DOC, DOCX</p>
              </div>
            )}
          </div>
        )}

        {/* Presentation Lesson Fields */}
        {newLesson.lesson_type === 'presentation' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-purple-50 rounded-lg">
            <div className="space-y-2">
              <Label>Content Type</Label>
              <Select
                value={newLesson.content_type || 'url'}
                onValueChange={(value) => updateNewLesson('content_type', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="url">External Link</SelectItem>
                  <SelectItem value="upload">Upload Slides</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>Number of Slides</Label>
              <Input
                type="number"
                value={newLesson.slide_count || ''}
                onChange={(e) => updateNewLesson('slide_count', e.target.value)}
                placeholder="20"
              />
            </div>
            {newLesson.content_type === 'url' ? (
              <div className="space-y-2 md:col-span-2">
                <Label>Presentation URL</Label>
                <Input
                  value={newLesson.content_url || ''}
                  onChange={(e) => updateNewLesson('content_url', e.target.value)}
                  placeholder="https://docs.google.com/presentation/... or https://slides.com/..."
                />
              </div>
            ) : (
              <div className="space-y-2 md:col-span-2">
                <Label>Upload Presentation</Label>
                <Input
                  type="file"
                  accept=".ppt,.pptx,.pdf"
                  onChange={(e) => updateNewLesson('uploaded_file', e.target.files?.[0] || null)}
                />
                <p className="text-xs text-muted-foreground">Supported formats: PPT, PPTX, PDF</p>
              </div>
            )}
          </div>
        )}

        {/* Quiz Lesson Fields */}
        {newLesson.lesson_type === 'quiz' && (
          <div className="grid grid-cols-1 gap-4 p-4 bg-orange-50 rounded-lg">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label>Passing Score (%)</Label>
                <Input
                  type="number"
                  min="0"
                  max="100"
                  value={newLesson.passing_score || ''}
                  onChange={(e) => updateNewLesson('passing_score', e.target.value)}
                  placeholder="70"
                />
              </div>
              <div className="space-y-2">
                <Label>Attempts Allowed</Label>
                <Input
                  type="number"
                  min="1"
                  max="10"
                  value={newLesson.attempts_allowed || ''}
                  onChange={(e) => updateNewLesson('attempts_allowed', e.target.value)}
                  placeholder="3"
                />
              </div>
              <div className="space-y-2">
                <Label>Time Limit (minutes)</Label>
                <Input
                  type="number"
                  min="1"
                  value={newLesson.time_limit || ''}
                  onChange={(e) => updateNewLesson('time_limit', e.target.value)}
                  placeholder="30"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>Quiz Instructions</Label>
              <Textarea
                value={newLesson.quiz_instructions || ''}
                onChange={(e) => updateNewLesson('quiz_instructions', e.target.value)}
                placeholder="Instructions for taking this quiz..."
                rows={2}
              />
            </div>

            <div className="space-y-2">
              <Label>Quiz Generation Method</Label>
              <Select
                value={newLesson.quiz_generation_method || 'ai'}
                onValueChange={(value) => updateNewLesson('quiz_generation_method', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ai">AI-Generated (Unique per user)</SelectItem>
                  <SelectItem value="manual">Manual Questions</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {newLesson.quiz_generation_method === 'ai' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-3 bg-white rounded border">
                <div className="space-y-2">
                  <Label>Number of Questions</Label>
                  <Input
                    type="number"
                    min="1"
                    max="50"
                    value={newLesson.quiz_question_count || ''}
                    onChange={(e) => updateNewLesson('quiz_question_count', e.target.value)}
                    placeholder="10"
                  />
                </div>
                <div className="space-y-2">
                  <Label>Question Types</Label>
                  <Select
                    value={newLesson.quiz_question_types || 'multiple_choice'}
                    onValueChange={(value) => updateNewLesson('quiz_question_types', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="multiple_choice">Multiple Choice</SelectItem>
                      <SelectItem value="mixed">Mixed Types</SelectItem>
                      <SelectItem value="true_false">True/False</SelectItem>
                      <SelectItem value="short_answer">Short Answer</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2 md:col-span-2">
                  <Label>Learning Objectives (for AI generation)</Label>
                  <Textarea
                    value={newLesson.quiz_learning_objectives || ''}
                    onChange={(e) => updateNewLesson('quiz_learning_objectives', e.target.value)}
                    placeholder="List the key concepts this quiz should test..."
                    rows={3}
                  />
                </div>
              </div>
            )}

            {newLesson.quiz_generation_method === 'manual' && (
              <div className="p-3 bg-white rounded border">
                <p className="text-sm text-muted-foreground mb-2">
                  Manual questions can be added after creating the lesson.
                </p>
              </div>
            )}
          </div>
        )}

        <div className="flex items-center space-x-2">
          <Button
            size="sm"
            onClick={handleAddLesson}
            disabled={loading || !newLesson.name.trim()}
          >
            <Save className="h-3 w-3 mr-2" />
            {loading ? 'Creating...' : 'Create Lesson'}
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={onCancel}
            disabled={loading}
          >
            <X className="h-3 w-3 mr-2" />
            Cancel
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
