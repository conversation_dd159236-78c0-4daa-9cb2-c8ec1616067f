"use client"

import type React from "react"

import { useState, useEffect, memo, useCallback } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { useRouter } from "next/navigation"
import {
  Settings,
  LayoutDashboard,
  Users,
  Building2,
  BookOpen,
  LogOut,
  Sun,
  Moon,
  User,
  FileText,
  Bell,
  ChevronLeft,
  ChevronRight,
  Menu,
  Brain,
  Route,
  GraduationCap
} from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useTheme } from "next-themes"
import { supabase } from "@/lib/supabase"
import { ContextSwitcher } from "@/components/luna/context-switcher"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarProvider,
  SidebarTrigger,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarGroupContent,
  SidebarInset,
} from "@/components/ui/sidebar"

interface NavItem {
  title: string
  href: string
  icon: React.ElementType
}

interface NavGroup {
  label: string
  items: NavItem[]
}

const dashboardItem: NavItem = {
  title: "Dashboard",
  href: "/admin",
  icon: LayoutDashboard,
}

const navGroups: NavGroup[] = [
  {
    label: "Account Management",
    items: [
      {
        title: "Organizations",
        href: "/admin/organizations",
        icon: Building2,
      },
      {
        title: "Users",
        href: "/admin/users",
        icon: Users,
      },
    ]
  },
  {
    label: "Learning Management",
    items: [
      {
        title: "Programs",
        href: "/admin/programs",
        icon: GraduationCap,
      },
      {
        title: "Pathways",
        href: "/admin/pathways",
        icon: Route,
      },
      {
        title: "Courses",
        href: "/admin/courses",
        icon: BookOpen,
      },
      {
        title: "Assessments",
        href: "/admin/assessments",
        icon: FileText,
      },
    ]
  },
  {
    label: "Settings",
    items: [
      {
        title: "Platform Configuration",
        href: "/admin/platform",
        icon: Brain,
      },
      {
        title: "Platform Settings",
        href: "/admin/settings",
        icon: Settings,
      },
    ]
  },
]

const AdminDashboardLayout = memo(function AdminDashboardLayout({ children }: { children: React.ReactNode }) {
  const [mounted, setMounted] = useState(false)
  const [userData, setUserData] = useState<{full_name: string; email: string} | null>(null)
  const [notificationCount, setNotificationCount] = useState(3) // Mock notification count
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const { theme, setTheme } = useTheme()
  const pathname = usePathname()
  const router = useRouter()

  const handleSignOut = useCallback(async () => {
    await supabase.auth.signOut()
    router.push('/login')
    router.refresh()
  }, [router])

  useEffect(() => {
    setMounted(true)

    // Check authentication and fetch user data
    const checkAuthAndFetchData = async () => {
      try {
        console.log('[ADMIN-LAYOUT] 🔍 Checking authentication...')

        const response = await fetch('/api/auth/user')
        const authData = await response.json()

        if (!authData.success || !authData.user) {
          console.log('[ADMIN-LAYOUT] ❌ Not authenticated, redirecting to login')
          router.push('/login')
          return
        }

        if (!authData.user.isPlatformAdmin) {
          console.log('[ADMIN-LAYOUT] ❌ Not a platform admin, redirecting to dashboard')
          router.push('/dashboard')
          return
        }

        console.log('[ADMIN-LAYOUT] ✅ Platform admin authenticated')
        setUserData({
          full_name: authData.user.full_name,
          email: authData.user.email
        })
        setIsAuthenticated(true)

      } catch (error) {
        console.error('[ADMIN-LAYOUT] ❌ Error checking authentication:', error)
        router.push('/login')
      } finally {
        setIsLoading(false)
      }
    }

    checkAuthAndFetchData()
  }, [router])

  if (!mounted || isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading admin dashboard...</p>
        </div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null // Will redirect in useEffect
  }

  // Get initials from full name
  const getInitials = (name: string) => {
    if (!name) return 'AD'
    return name.split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2)
  }

  // Get page title from pathname
  const getPageTitle = () => {
    switch (pathname) {
      case '/admin':
        return 'Admin Dashboard'
      case '/admin/users':
        return 'User Management'
      case '/admin/organizations':
        return 'Organization Management'
      case '/admin/training-modules':
        return 'Courses'
      case '/admin/assessments':
        return 'Assessment Management'
      case '/admin/career-pathways':
        return 'Career Pathways'
      case '/admin/platform':
        return 'Platform Configuration'
      case '/admin/settings':
        return 'Platform Settings'
      case '/admin/account':
        return 'Account Settings'
      case '/admin/bpos':
        return 'BPO Companies'
      default:
        return 'Admin'
    }
  }

  return (
    <SidebarProvider>
      <div className="flex h-screen w-full">
        {/* Collapsible Sidebar */}
        <Sidebar
          className="border-r"
          collapsible="icon"
          style={{
            '--sidebar-width-icon': '5rem', // 80px for collapsed state
          } as React.CSSProperties}
        >
          <SidebarHeader className="px-6 py-4 h-auto flex items-center mb-5">
            <Link href="/admin" className="flex items-center group-data-[collapsible=icon]:justify-center">
              <img
                src="/Luna_Logo_Caps_Final.png"
                alt="Luna Logo"
                className="h-16 w-auto group-data-[collapsible=icon]:hidden"
              />
              <img
                src="/Luna_Favicon_Caps.jpg"
                alt="Luna"
                className="h-10 w-10 rounded-lg hidden group-data-[collapsible=icon]:block"
              />
            </Link>
          </SidebarHeader>

          <SidebarContent>
            {/* Dashboard - Standalone & Prominent */}
            <SidebarGroup>
              <SidebarGroupContent>
                <SidebarMenu>
                  <SidebarMenuItem>
                    <SidebarMenuButton
                      asChild
                      isActive={pathname === dashboardItem.href}
                      className="w-full group-data-[collapsible=icon]:w-full group-data-[collapsible=icon]:justify-center group-data-[collapsible=icon]:px-0 bg-primary/10 hover:bg-primary/20 border border-primary/20 font-medium text-primary hover:text-primary mb-4"
                      tooltip={dashboardItem.title}
                    >
                      <Link href={dashboardItem.href} className="flex items-center gap-3 group-data-[collapsible=icon]:justify-center">
                        <dashboardItem.icon className="h-5 w-5 shrink-0" />
                        <span className="group-data-[collapsible=icon]:hidden font-semibold">{dashboardItem.title}</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>

            {/* Navigation Groups */}
            {navGroups.map((group) => (
              <SidebarGroup key={group.label}>
                <SidebarGroupLabel className="px-3 text-xs font-medium uppercase tracking-wider text-muted-foreground group-data-[collapsible=icon]:hidden">
                  {group.label}
                </SidebarGroupLabel>
                <SidebarGroupContent>
                  <SidebarMenu>
                    {group.items.map((item) => (
                      <SidebarMenuItem key={item.href}>
                        <SidebarMenuButton
                          asChild
                          isActive={pathname === item.href}
                          className="w-full group-data-[collapsible=icon]:w-full group-data-[collapsible=icon]:justify-center group-data-[collapsible=icon]:px-0"
                          tooltip={item.title}
                        >
                          <Link href={item.href} className="flex items-center gap-3 group-data-[collapsible=icon]:justify-center">
                            <item.icon className="h-5 w-5 shrink-0" />
                            <span className="group-data-[collapsible=icon]:hidden">{item.title}</span>
                          </Link>
                        </SidebarMenuButton>
                      </SidebarMenuItem>
                    ))}
                  </SidebarMenu>
                </SidebarGroupContent>
              </SidebarGroup>
            ))}
          </SidebarContent>

          <SidebarFooter className="border-t p-4 group-data-[collapsible=icon]:px-2 group-data-[collapsible=icon]:py-3">
            <div className="text-xs text-muted-foreground group-data-[collapsible=icon]:hidden space-y-1">
              <div className="font-medium text-foreground">Luna v1.3</div>
              <div className="text-[10px] leading-tight">
                © 2025 Black Amber Technologies.<br />
                All rights reserved.
              </div>
            </div>
            <div className="hidden group-data-[collapsible=icon]:flex group-data-[collapsible=icon]:justify-center">
              <div className="text-[10px] text-muted-foreground font-medium">
                v1.3
              </div>
            </div>
          </SidebarFooter>
        </Sidebar>

        {/* Main Content Area */}
        <SidebarInset className="flex flex-col flex-1">
          {/* Header */}
          <header className="flex h-16 shrink-0 items-center gap-2 border-b bg-background px-6">
            <SidebarTrigger className="z-10 group-data-[state=collapsed]:ml-[30px]" />
            <Separator orientation="vertical" className="mr-2 h-4" />

            {/* Page Title */}
            <h1 className="text-xl font-semibold text-foreground flex-1">
              {getPageTitle()}
            </h1>

            {/* Header Actions */}
            <div className="flex items-center gap-2">
              {/* Theme Toggle */}
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
                className="h-9 w-9"
              >
                {theme === "dark" ? (
                  <Sun className="h-4 w-4" />
                ) : (
                  <Moon className="h-4 w-4" />
                )}
              </Button>

              {/* Notifications */}
              <Button variant="ghost" size="icon" className="h-9 w-9 relative">
                <Bell className="h-4 w-4" />
                {notificationCount > 0 && (
                  <Badge
                    variant="destructive"
                    className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 text-xs flex items-center justify-center"
                  >
                    {notificationCount}
                  </Badge>
                )}
              </Button>

              {/* User Menu */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="h-9 w-9 rounded-full p-0">
                    <Avatar className="h-8 w-8">
                      <AvatarFallback className="bg-primary text-primary-foreground text-sm">
                        {userData ? getInitials(userData.full_name) : 'AD'}
                      </AvatarFallback>
                    </Avatar>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuLabel>
                    <div className="flex flex-col space-y-1">
                      <p className="text-sm font-medium">
                        {userData?.full_name || 'Admin User'}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {userData?.email || '<EMAIL>'}
                      </p>
                    </div>
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => router.push('/admin/account')}>
                    <User className="mr-2 h-4 w-4" />
                    <span>Account Settings</span>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleSignOut}>
                    <LogOut className="mr-2 h-4 w-4" />
                    <span>Log Out</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </header>

          {/* Main Content */}
          <main className="flex-1 overflow-auto bg-muted/30">
            {children}
          </main>
        </SidebarInset>
      </div>
    </SidebarProvider>
  )
})

export default AdminDashboardLayout;