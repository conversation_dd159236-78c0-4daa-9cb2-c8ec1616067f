import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { Database } from '@/types/supabase'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const supabase = createRouteHandlerClient<Database>({ cookies })

    // Check if user is authenticated
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Fetch lesson name
    const { data: lesson, error } = await supabase
      .from('course_lessons')
      .select('name')
      .eq('id', id)
      .single()

    if (error || !lesson) {
      return NextResponse.json({ error: 'Lesson not found' }, { status: 404 })
    }

    return NextResponse.json({ lessonName: lesson.name })
  } catch (error) {
    console.error('Error fetching lesson breadcrumb:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
