"use client"

import * as React from "react"
import { useState, useEffect } from "react"
import { usePathname, useRouter } from "next/navigation"
import Image from "next/image"
import Link from "next/link"
import {
  BookOpen,
  Brain,
  Calendar,
  ClipboardCheck,
  FileCheck,
  Home,
  Settings,
  User,
  Briefcase,
  HelpCircle,
  Phone,
  ShoppingCart,
  GraduationCap,
  LogOut,
  Bell,
  Sun,
  Moon,
  LayoutDashboard,
  BarChart3,
  Users,
  Building2,
  Shield,
  Route,
} from "lucide-react"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarInset,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarProvider,
  SidebarRail,
  SidebarTrigger,
} from "@/components/ui/sidebar"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { <PERSON><PERSON><PERSON>and<PERSON> } from "@/components/ui/scroll-handler"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { UserAvatar } from "@/components/ui/user-avatar"
import { ContextSwitcher } from "@/components/luna/context-switcher-sidebar"
import { Separator } from "@/components/ui/separator"
import { useTheme } from "next-themes"
import { useLunaAuth } from "@/hooks/use-luna-auth"
import { DynamicCourseBreadcrumb } from "@/components/dynamic-course-breadcrumb"

interface NavItem {
  title: string
  href: string
  icon: React.ElementType
  badge?: number
  conditional?: boolean
}

interface NavSection {
  title: string
  items: NavItem[]
}

// Helper function to get breadcrumb title from pathname and context
function getBreadcrumbTitle(pathname: string, context: 'individual' | 'organization'): string {
  const pathSegments = pathname.split('/').filter(Boolean)
  const lastSegment = pathSegments[pathSegments.length - 1]

  const individualBreadcrumbMap: Record<string, string> = {
    'individual': 'Dashboard',
    'profile': 'Profile',
    'training': 'Learning Paths',
    'courses-marketplace': 'Courses Marketplace',
    'courses': 'Courses',
    'modules': 'Module',
    'lessons': 'Lesson',
    'role-call-training': 'Role-Call Training',
    'assessments': 'Assessments',
    'files': 'Files & Certificates',
    'job-board': 'Job Board',
    'applications': 'My Applications',
    'interviews': 'Interviews',
    'settings': 'Settings',
    'help': 'Help & Support',
    'account': 'Account'
  }

  const organizationBreadcrumbMap: Record<string, string> = {
    'organization': 'Dashboard',
    'departments': 'Departments',
    'staff': 'Staff',
    'job-roles': 'Job Roles',
    'vacancies': 'Vacancies',
    'candidates': 'Candidates',
    'interviews': 'Interviews',
    'business-profile': 'Business Profile',
  }

  // Handle dynamic department routes
  if (context === 'organization' && pathSegments.includes('departments')) {
    const deptIndex = pathSegments.indexOf('departments')

    // /org/[orgSlug]/departments/[departmentSlug]/staff
    if (pathSegments.length > deptIndex + 2 && pathSegments[deptIndex + 2] === 'staff') {
      const departmentSlug = pathSegments[deptIndex + 1]
      const departmentName = departmentSlug.split('-').map(word =>
        word.charAt(0).toUpperCase() + word.slice(1)
      ).join(' ')

      // /org/[orgSlug]/departments/[departmentSlug]/staff/[staffSlug]/profile
      if (pathSegments.length > deptIndex + 4 && pathSegments[deptIndex + 4] === 'profile') {
        const staffSlug = pathSegments[deptIndex + 3]
        const staffName = staffSlug.split('-').map(word =>
          word.charAt(0).toUpperCase() + word.slice(1)
        ).join(' ')
        return staffName
      }

      return departmentName
    }
  }

  const breadcrumbMap = context === 'organization' ? organizationBreadcrumbMap : individualBreadcrumbMap
  return breadcrumbMap[lastSegment] || 'Dashboard'
}

// Helper function to get context name for breadcrumb
function getContextName(context: 'individual' | 'organization'): string {
  return context === 'organization' ? 'Organization' : 'Individual'
}

// Helper function to get breadcrumb items with proper hierarchy
function getBreadcrumbItems(pathname: string, context: 'individual' | 'organization') {
  const pathSegments = pathname.split('/').filter(Boolean)

  // Handle course routes for individual context
  if (context === 'individual' && pathSegments.includes('courses')) {
    const courseIndex = pathSegments.indexOf('courses')

    // /individual/courses/[id] - Course Details
    if (pathSegments.length === courseIndex + 2) {
      return (
        <>
          <BreadcrumbItem>
            <BreadcrumbLink href="/individual/courses-marketplace" className="text-base font-medium">
              Courses
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <DynamicCourseBreadcrumb
              type="course"
              courseId={pathSegments[courseIndex + 1]}
            />
          </BreadcrumbItem>
        </>
      )
    }

    // /individual/courses/[id]/modules/[moduleId] - Module Details
    if (pathSegments.length === courseIndex + 4 && pathSegments[courseIndex + 2] === 'modules') {
      return (
        <>
          <BreadcrumbItem>
            <BreadcrumbLink href="/individual/courses-marketplace" className="text-base font-medium">
              Courses
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <DynamicCourseBreadcrumb
              type="course-link"
              courseId={pathSegments[courseIndex + 1]}
            />
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <DynamicCourseBreadcrumb
              type="module"
              courseId={pathSegments[courseIndex + 1]}
              moduleId={pathSegments[courseIndex + 3]}
            />
          </BreadcrumbItem>
        </>
      )
    }

    // /individual/courses/[id]/modules/[moduleId]/lessons/[lessonId] - Lesson Details
    if (pathSegments.length === courseIndex + 6 && pathSegments[courseIndex + 2] === 'modules' && pathSegments[courseIndex + 4] === 'lessons') {
      return (
        <>
          <BreadcrumbItem>
            <BreadcrumbLink href="/individual/courses-marketplace" className="text-base font-medium">
              Courses
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <DynamicCourseBreadcrumb
              type="course-link"
              courseId={pathSegments[courseIndex + 1]}
            />
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <DynamicCourseBreadcrumb
              type="module-link"
              courseId={pathSegments[courseIndex + 1]}
              moduleId={pathSegments[courseIndex + 3]}
            />
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <DynamicCourseBreadcrumb
              type="lesson"
              courseId={pathSegments[courseIndex + 1]}
              moduleId={pathSegments[courseIndex + 3]}
              lessonId={pathSegments[courseIndex + 5]}
            />
          </BreadcrumbItem>
        </>
      )
    }
  }

  // Handle dynamic department routes
  if (context === 'organization' && pathSegments.includes('departments')) {
    const deptIndex = pathSegments.indexOf('departments')

    // /org/[orgSlug]/departments/[departmentSlug]/staff
    if (pathSegments.length > deptIndex + 2 && pathSegments[deptIndex + 2] === 'staff') {
      const departmentSlug = pathSegments[deptIndex + 1]
      const departmentName = departmentSlug.split('-').map(word =>
        word.charAt(0).toUpperCase() + word.slice(1)
      ).join(' ')

      // /org/[orgSlug]/departments/[departmentSlug]/staff/[staffSlug]/profile
      if (pathSegments.length > deptIndex + 4 && pathSegments[deptIndex + 4] === 'profile') {
        const staffSlug = pathSegments[deptIndex + 3]
        const staffName = staffSlug.split('-').map(word =>
          word.charAt(0).toUpperCase() + word.slice(1)
        ).join(' ')

        return (
          <>
            <BreadcrumbItem>
              <BreadcrumbLink href={`/${pathSegments.slice(0, deptIndex + 1).join('/')}`} className="text-base font-medium">
                Department
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href={`/${pathSegments.slice(0, deptIndex + 3).join('/')}`} className="text-base font-medium">
                {departmentName}
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage className="text-base font-semibold text-foreground">{staffName}</BreadcrumbPage>
            </BreadcrumbItem>
          </>
        )
      }

      // /org/[orgSlug]/departments/[departmentSlug]/staff
      return (
        <>
          <BreadcrumbItem>
            <BreadcrumbLink href={`/${pathSegments.slice(0, deptIndex + 1).join('/')}`} className="text-base font-medium">
              Department
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage className="text-base font-semibold text-foreground">{departmentName}</BreadcrumbPage>
          </BreadcrumbItem>
        </>
      )
    }
  }

  // Default single breadcrumb item
  return (
    <BreadcrumbItem>
      <BreadcrumbPage className="text-base font-semibold text-foreground">{getBreadcrumbTitle(pathname, context)}</BreadcrumbPage>
    </BreadcrumbItem>
  )
}

interface SidebarLayoutProps {
  children: React.ReactNode
  context?: 'individual' | 'organization'
}

export function IndividualSidebarLayout({ children }: { children: React.ReactNode }) {
  return <UnifiedSidebarLayout context="individual">{children}</UnifiedSidebarLayout>
}

export function OrganizationSidebarLayout({ children }: { children: React.ReactNode }) {
  return <UnifiedSidebarLayout context="organization">{children}</UnifiedSidebarLayout>
}

export function UnifiedSidebarLayout({ children, context = 'individual' }: SidebarLayoutProps) {
  const [mounted, setMounted] = useState(false)
  const [isInBPOProgram, setIsInBPOProgram] = useState(false)
  const { user, loading, signOut } = useLunaAuth()
  const { theme, setTheme } = useTheme()
  const pathname = usePathname()
  const router = useRouter()

  useEffect(() => {
    setMounted(true)

    // Debug user data
    console.log('[SIDEBAR] User data:', {
      hasUser: !!user,
      userId: user?.id,
      fullName: user?.full_name,
      email: user?.email,
      avatarUrl: user?.avatar_url,
      loading
    })

    // Check if user is in organizational training program when user data is available
    const checkOrganizationalProgram = async () => {
      if (user?.id) {
        try {
          const { createClientComponentClient } = await import('@supabase/auth-helpers-nextjs')
          const supabase = createClientComponentClient()

          const { data: trainingData } = await supabase
            .from('user_training_data')
            .select('metadata')
            .eq('user_id', user.id)
            .eq('training_context', 'organization')
            .limit(1)

          setIsInBPOProgram(trainingData && trainingData.length > 0)
        } catch (error) {
          console.error('Error checking organizational program:', error)
          setIsInBPOProgram(false)
        }
      }
    }

    checkOrganizationalProgram()
  }, [user?.id, user?.full_name, user?.email, loading])

  // Dynamic navigation based on context
  const getNavigationSections = (context: 'individual' | 'organization'): NavSection[] => {
    // Helper function to get current organization slug
    const getCurrentOrgSlug = () => {
      if (user?.currentContext?.type === 'organization') {
        return user?.employmentRelationships.find(emp =>
          emp.organization_id === user?.currentContext?.organization_id
        )?.organization_slug || 'default'
      }
      return 'default'
    }

    if (context === 'organization') {
      return [
        {
          title: "Dashboard",
          items: [
            {
              title: "Dashboard",
              href: `/org/${getCurrentOrgSlug()}`,
              icon: LayoutDashboard,
            },
          ],
        },
        {
          title: "Company Structure",
          items: [
            {
              title: "Departments",
              href: `/org/${getCurrentOrgSlug()}/departments`,
              icon: Building2,
              badge: 5,
            },
            {
              title: "Staff",
              href: `/org/${getCurrentOrgSlug()}/staff`,
              icon: Users,
              badge: 24,
            },
            {
              title: "Job Roles",
              href: `/org/${getCurrentOrgSlug()}/job-roles`,
              icon: Briefcase,
            },
          ],
        },
        {
          title: "Talent Acquisition",
          items: [
            {
              title: "Vacancies",
              href: `/org/${getCurrentOrgSlug()}/vacancies`,
              icon: ClipboardCheck,
              badge: 3,
            },
            {
              title: "Candidates",
              href: `/org/${getCurrentOrgSlug()}/candidates`,
              icon: User,
              badge: 12,
            },
            {
              title: "Interviews",
              href: `/org/${getCurrentOrgSlug()}/interviews`,
              icon: Calendar,
              badge: 7,
            },
          ],
        },
        {
          title: "Profile & Settings",
          items: [
            {
              title: "Business Profile",
              href: `/org/${getCurrentOrgSlug()}/business-profile`,
              icon: Settings,
            },
          ],
        },
      ]
    }

    // Individual context navigation (existing)
    return [
    {
      title: "Learning",
      items: [
        {
          title: "Dashboard",
          href: "/individual",
          icon: Home,
        },
        {
          title: "Profile",
          href: "/individual/profile",
          icon: User,
        },
        {
          title: "Learning Paths",
          href: "/individual/training",
          icon: BookOpen,
          badge: 7,
        },
        {
          title: "Courses Marketplace",
          href: "/individual/courses-marketplace",
          icon: ShoppingCart,
        },
        {
          title: "Role-Call Training",
          href: "/individual/role-call-training",
          icon: Phone,
          conditional: isInBPOProgram,
        },
        {
          title: "Assessments",
          href: "/individual/assessments",
          icon: ClipboardCheck,
          badge: 0,
        },
        {
          title: "Files & Certificates",
          href: "/individual/files",
          icon: FileCheck,
        },
      ],
    },
    {
      title: "Career",
      items: [
        {
          title: "Job Board",
          href: "/individual/job-board",
          icon: Briefcase,
          badge: 0,
        },
        {
          title: "My Applications",
          href: "/individual/applications",
          icon: ClipboardCheck,
          badge: 0,
        },
        {
          title: "Interviews",
          href: "/individual/interviews",
          icon: Calendar,
          badge: 0,
        },
      ],
    },
    {
      title: "Support",
      items: [
        {
          title: "Settings",
          href: "/individual/settings",
          icon: Settings,
        },
        {
          title: "Help & Support",
          href: "/individual/help",
          icon: HelpCircle,
        },
      ],
    },
  ]
  }

  const navSections = getNavigationSections(context)

  const handleLogout = async () => {
    try {
      await signOut()
    } catch (error) {
      console.error('Logout error:', error)
      // Fallback to direct navigation
      window.location.href = '/login'
    }
  }

  if (!mounted || loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <TooltipProvider>
      <ScrollHandler />
      <SidebarProvider>
      <Sidebar variant="sidebar" collapsible="icon" className="bg-white border-r border-border">
        <SidebarHeader>
          <SidebarMenu>
            <SidebarMenuItem>
              <div className="flex justify-center px-4 py-6">
                <Image
                  src="/Luna_Logo_Caps_Final.png"
                  alt="Luna Platform"
                  width={160}
                  height={50}
                  className="object-contain w-full max-w-full group-data-[collapsible=icon]:hidden"
                />
                <Image
                  src="/Luna_Logo_Caps_Final.png"
                  alt="Luna"
                  width={32}
                  height={32}
                  className="object-contain hidden group-data-[collapsible=icon]:block"
                />
              </div>
            </SidebarMenuItem>
          </SidebarMenu>

          {/* Native Context Switcher with enhanced styling */}
          <SidebarMenu>
            <SidebarMenuItem>
              <ContextSwitcher />
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarHeader>

        <SidebarContent>
          {navSections.map((section) => (
            <SidebarGroup key={section.title}>
              <SidebarGroupLabel
                className="uppercase tracking-wide"
                style={{
                  color: '#6B7280',
                  fontSize: '11px',
                  fontWeight: '500'
                }}
              >
                {section.title}
              </SidebarGroupLabel>
              <SidebarGroupContent>
                <SidebarMenu>
                  {section.items
                    .filter(item => item.conditional !== false)
                    .map((item) => (
                    <SidebarMenuItem key={item.title}>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <SidebarMenuButton asChild className="mb-1 hover:bg-transparent active:bg-transparent data-[active=true]:bg-transparent">
                            <Link href={item.href} className="flex items-center gap-3 p-0">
                              <item.icon className="h-5 w-5" style={{ width: '20px', height: '20px', color: '#6B7280' }} />
                              <span
                                className="font-normal"
                                style={{
                                  color: '#374151',
                                  fontSize: '14px',
                                  fontWeight: '400'
                                }}
                              >
                                {item.title}
                              </span>
                              {item.badge !== undefined && (
                                <span className="ml-auto text-xs text-muted-foreground font-medium">
                                  {item.badge}
                                </span>
                              )}
                            </Link>
                          </SidebarMenuButton>
                        </TooltipTrigger>
                        <TooltipContent side="right" className="group-data-[collapsible=icon]:block hidden">
                          {item.title}
                        </TooltipContent>
                      </Tooltip>
                    </SidebarMenuItem>
                  ))}
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>
          ))}
        </SidebarContent>

        <SidebarFooter>
          <div className="px-4 py-3 border-t border-sidebar-border group-data-[collapsible=icon]:hidden">
            <div className="space-y-1" style={{ fontSize: '12px', color: '#444444' }}>
              <div style={{ fontWeight: '600' }}>Luna v1.3</div>
              <div style={{ fontWeight: '400' }}>© 2025 Black Amber Technologies.</div>
              <div style={{ fontWeight: '400' }}>All rights reserved.</div>
            </div>
          </div>
        </SidebarFooter>
        <SidebarRail />
      </Sidebar>
      <SidebarInset className="flex flex-col flex-1" style={{ backgroundColor: '#f9fafb' }}>
        <header className="sticky top-0 z-50 flex h-16 shrink-0 items-center gap-2 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
          <div className="flex items-center gap-3 px-6">
            <SidebarTrigger className="-ml-1 h-6 w-6" />
            <Separator orientation="vertical" className="mr-2 h-5" />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href={context === 'organization' ? '/organization' : '/individual'} className="text-base font-medium">
                    {getContextName(context)}
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                {getBreadcrumbItems(pathname, context)}
              </BreadcrumbList>
            </Breadcrumb>
          </div>
          <div className="ml-auto flex items-center gap-3 px-6">
            {/* Notifications */}
            <Button variant="ghost" size="icon" className="h-9 w-9">
              <Bell className="h-5 w-5" style={{ color: '#6B7280' }} />
              <span className="sr-only">Notifications</span>
            </Button>

            {/* Theme Toggle */}
            <Button
              variant="ghost"
              size="icon"
              className="h-9 w-9"
              onClick={() => setTheme(theme === "light" ? "dark" : "light")}
            >
              {theme === "light" ? <Moon className="h-5 w-5" style={{ color: '#6B7280' }} /> : <Sun className="h-5 w-5" style={{ color: '#6B7280' }} />}
              <span className="sr-only">Toggle theme</span>
            </Button>

            {/* User Avatar Dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="relative h-9 w-9 rounded-full">
                  <UserAvatar
                    user={{
                      full_name: user?.full_name || 'User',
                      email: user?.email || '',
                      avatar_url: user?.avatar_url
                    }}
                    size="md"
                  />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="end" forceMount>
                <DropdownMenuLabel className="font-normal p-3">
                  <div className="flex flex-col space-y-1">
                    <p className="text-sm font-medium leading-none">
                      {user?.full_name || 'User'}
                    </p>
                    <p className="text-xs leading-none text-muted-foreground">
                      {user?.email || 'No email'}
                    </p>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem className="px-3 py-2">
                  <User className="mr-2 h-4 w-4" style={{ color: '#6B7280' }} />
                  <span className="text-sm">Account Settings</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleLogout} className="px-3 py-2">
                  <LogOut className="mr-2 h-4 w-4" style={{ color: '#6B7280' }} />
                  <span className="text-sm">Log Out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </header>
        <main className="flex-1 overflow-auto" data-scrollable style={{ backgroundColor: '#f9fafb' }}>
          {children}
        </main>
      </SidebarInset>
    </SidebarProvider>
    </TooltipProvider>
  )
}
