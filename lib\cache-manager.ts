/**
 * Advanced Caching System
 * Multi-layer caching with TTL, invalidation, and persistence
 * Server-side compatible caching system
 */

import { LRUCache } from 'lru-cache'

// =============================================================================
// CACHE CONFIGURATION
// =============================================================================

interface CacheConfig {
  maxSize: number
  ttl: number // Time to live in milliseconds
  staleWhileRevalidate?: number
  persistToStorage?: boolean
  storageKey?: string
}

interface CacheEntry<T> {
  data: T
  timestamp: number
  ttl: number
  staleWhileRevalidate?: number
}

interface CacheStats {
  hits: number
  misses: number
  sets: number
  deletes: number
  size: number
  hitRate: number
}

// =============================================================================
// CACHE MANAGER CLASS
// =============================================================================

export class CacheManager<T = any> {
  private cache: LRUCache<string, CacheEntry<T>>
  private config: CacheConfig
  private stats: CacheStats
  private persistenceEnabled: boolean

  constructor(config: CacheConfig) {
    this.config = config
    // DISABLED: Storage persistence was causing lag
    // Only enable in production with explicit flag
    this.persistenceEnabled = config.persistToStorage &&
                              typeof window !== 'undefined' &&
                              process.env.NODE_ENV === 'production' &&
                              process.env.NEXT_PUBLIC_ENABLE_CACHE_PERSISTENCE === 'true'

    this.cache = new LRUCache({
      max: config.maxSize,
      dispose: (value, key) => {
        if (this.persistenceEnabled) {
          this.removeFromStorage(key)
        }
      }
    })

    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      size: 0,
      hitRate: 0
    }

    // Load from storage if enabled
    if (this.persistenceEnabled) {
      this.loadFromStorage()
    }
  }

  /**
   * Get item from cache with automatic stale-while-revalidate
   */
  async get(
    key: string, 
    fetcher?: () => Promise<T> | T,
    customTtl?: number
  ): Promise<T | null> {
    const entry = this.cache.get(key)
    const now = Date.now()

    // Cache hit - check if still valid
    if (entry) {
      const age = now - entry.timestamp
      const isExpired = age > entry.ttl
      const isStale = entry.staleWhileRevalidate && age > entry.staleWhileRevalidate

      if (!isExpired) {
        this.stats.hits++
        this.updateStats()
        return entry.data
      }

      // Stale-while-revalidate: return stale data and refresh in background
      if (isStale && fetcher) {
        this.stats.hits++
        this.updateStats()
        
        // Refresh in background
        this.refreshInBackground(key, fetcher, customTtl)
        return entry.data
      }

      // Expired - remove from cache
      this.cache.delete(key)
      if (this.persistenceEnabled) {
        this.removeFromStorage(key)
      }
    }

    // Cache miss - fetch new data if fetcher provided
    if (fetcher) {
      this.stats.misses++
      this.updateStats()
      
      try {
        const data = await fetcher()
        this.set(key, data, customTtl)
        return data
      } catch (error) {
        console.error(`Cache fetch error for key ${key}:`, error)
        return null
      }
    }

    this.stats.misses++
    this.updateStats()
    return null
  }

  /**
   * Set item in cache
   */
  set(key: string, data: T, customTtl?: number): void {
    const ttl = customTtl || this.config.ttl
    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl,
      staleWhileRevalidate: this.config.staleWhileRevalidate
    }

    this.cache.set(key, entry)
    this.stats.sets++
    this.stats.size = this.cache.size
    this.updateStats()

    // Persist to storage if enabled
    if (this.persistenceEnabled) {
      this.saveToStorage(key, entry)
    }
  }

  /**
   * Delete item from cache
   */
  delete(key: string): boolean {
    const deleted = this.cache.delete(key)
    if (deleted) {
      this.stats.deletes++
      this.stats.size = this.cache.size
      this.updateStats()

      if (this.persistenceEnabled) {
        this.removeFromStorage(key)
      }
    }
    return deleted
  }

  /**
   * Clear entire cache
   */
  clear(): void {
    this.cache.clear()
    this.stats.size = 0
    this.updateStats()

    if (this.persistenceEnabled) {
      this.clearStorage()
    }
  }

  /**
   * Check if key exists and is not expired
   */
  has(key: string): boolean {
    const entry = this.cache.get(key)
    if (!entry) return false

    const age = Date.now() - entry.timestamp
    const isExpired = age > entry.ttl

    if (isExpired) {
      this.cache.delete(key)
      if (this.persistenceEnabled) {
        this.removeFromStorage(key)
      }
      return false
    }

    return true
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    return { ...this.stats }
  }

  /**
   * Invalidate cache entries by pattern
   */
  invalidatePattern(pattern: string | RegExp): number {
    let count = 0
    const regex = typeof pattern === 'string' ? new RegExp(pattern) : pattern

    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        this.delete(key)
        count++
      }
    }

    return count
  }

  /**
   * Preload data into cache
   */
  async preload(entries: Array<{ key: string; fetcher: () => Promise<T> | T; ttl?: number }>): Promise<void> {
    const promises = entries.map(async ({ key, fetcher, ttl }) => {
      try {
        const data = await fetcher()
        this.set(key, data, ttl)
      } catch (error) {
        console.error(`Preload error for key ${key}:`, error)
      }
    })

    await Promise.allSettled(promises)
  }

  // =============================================================================
  // PRIVATE METHODS
  // =============================================================================

  private async refreshInBackground(key: string, fetcher: () => Promise<T> | T, customTtl?: number): Promise<void> {
    try {
      const data = await fetcher()
      this.set(key, data, customTtl)
    } catch (error) {
      console.error(`Background refresh error for key ${key}:`, error)
    }
  }

  private updateStats(): void {
    const total = this.stats.hits + this.stats.misses
    this.stats.hitRate = total > 0 ? (this.stats.hits / total) * 100 : 0
  }

  private saveToStorage(key: string, entry: CacheEntry<T>): void {
    if (!this.config.storageKey || typeof window === 'undefined') return

    try {
      const storageKey = `${this.config.storageKey}:${key}`
      localStorage.setItem(storageKey, JSON.stringify(entry))
    } catch (error) {
      console.warn('Failed to save to localStorage:', error)
    }
  }

  private removeFromStorage(key: string): void {
    if (!this.config.storageKey || typeof window === 'undefined') return

    try {
      const storageKey = `${this.config.storageKey}:${key}`
      localStorage.removeItem(storageKey)
    } catch (error) {
      console.warn('Failed to remove from localStorage:', error)
    }
  }

  private loadFromStorage(): void {
    if (!this.config.storageKey || typeof window === 'undefined') return

    try {
      const prefix = `${this.config.storageKey}:`
      const now = Date.now()

      for (let i = 0; i < localStorage.length; i++) {
        const storageKey = localStorage.key(i)
        if (!storageKey?.startsWith(prefix)) continue

        const key = storageKey.substring(prefix.length)
        const entryJson = localStorage.getItem(storageKey)
        if (!entryJson) continue

        const entry: CacheEntry<T> = JSON.parse(entryJson)
        const age = now - entry.timestamp

        // Only load if not expired
        if (age <= entry.ttl) {
          this.cache.set(key, entry)
        } else {
          localStorage.removeItem(storageKey)
        }
      }
    } catch (error) {
      console.warn('Failed to load from localStorage:', error)
    }
  }

  private clearStorage(): void {
    if (!this.config.storageKey || typeof window === 'undefined') return

    try {
      const prefix = `${this.config.storageKey}:`
      const keysToRemove: string[] = []

      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key?.startsWith(prefix)) {
          keysToRemove.push(key)
        }
      }

      keysToRemove.forEach(key => localStorage.removeItem(key))
    } catch (error) {
      console.warn('Failed to clear localStorage:', error)
    }
  }
}

// =============================================================================
// CACHE INSTANCES
// =============================================================================

// Training modules cache
export const trainingCache = new CacheManager({
  maxSize: 100,
  ttl: 5 * 60 * 1000, // 5 minutes
  staleWhileRevalidate: 2 * 60 * 1000, // 2 minutes
  persistToStorage: true,
  storageKey: 'bpo-training-cache'
})

// User data cache
export const userCache = new CacheManager({
  maxSize: 50,
  ttl: 10 * 60 * 1000, // 10 minutes
  staleWhileRevalidate: 5 * 60 * 1000, // 5 minutes
  persistToStorage: true,
  storageKey: 'bpo-user-cache'
})

// API response cache
export const apiCache = new CacheManager({
  maxSize: 200,
  ttl: 2 * 60 * 1000, // 2 minutes
  staleWhileRevalidate: 1 * 60 * 1000, // 1 minute
  persistToStorage: false
})

// Static content cache (longer TTL)
export const staticCache = new CacheManager({
  maxSize: 50,
  ttl: 60 * 60 * 1000, // 1 hour
  staleWhileRevalidate: 30 * 60 * 1000, // 30 minutes
  persistToStorage: true,
  storageKey: 'bpo-static-cache'
})

// =============================================================================
// CACHE UTILITIES
// =============================================================================

/**
 * Create a cache key from multiple parts
 */
export function createCacheKey(...parts: (string | number | boolean | null | undefined)[]): string {
  return parts
    .filter(part => part !== null && part !== undefined)
    .map(part => String(part))
    .join(':')
}

/**
 * Cache decorator for functions
 */
export function cached<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  cache: CacheManager,
  keyGenerator: (...args: Parameters<T>) => string,
  ttl?: number
): T {
  return (async (...args: Parameters<T>) => {
    const key = keyGenerator(...args)
    return cache.get(key, () => fn(...args), ttl)
  }) as T
}

/**
 * Invalidate all caches
 */
export function invalidateAllCaches(): void {
  trainingCache.clear()
  userCache.clear()
  apiCache.clear()
  staticCache.clear()
}

/**
 * Get cache statistics for all caches
 */
export function getAllCacheStats() {
  return {
    training: trainingCache.getStats(),
    user: userCache.getStats(),
    api: apiCache.getStats(),
    static: staticCache.getStats()
  }
}
