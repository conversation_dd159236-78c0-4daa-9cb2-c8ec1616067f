'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  ArrowLeft,
  Clock,
  Users,
  Layout,
  BarChart,
  CheckCircle,
  PlayCircle,
  FileQuestion,
  Video,
  AlertCircle,
  FileText,
  ChevronRight,
  Play,
  Lock
} from 'lucide-react'
import { toast } from "sonner"
import { RichTextDisplay } from "@/components/ui/rich-text-display"

interface ModuleDetailsPageProps {
  module: {
    id: string
    name: string
    description: string
    estimatedDuration: number
    price?: number
    lessons: Array<{
      id: string
      name: string
      description: string
      lessonType: string
      sequenceOrder: number
      estimatedDuration: number
      isMandatory: boolean
      status: string
    }>
  }
  courseId: string
  courseName: string
  isEnrolled?: boolean
  enrollment?: {
    id: string
    enrolledAt: string
    startedAt?: string | null
    completedAt?: string | null
    progressPercentage: number
    currentLessonId?: string | null
  } | null
}

export default function ModuleDetailsPage({ 
  module, 
  courseId, 
  courseName, 
  isEnrolled = false, 
  enrollment 
}: ModuleDetailsPageProps) {
  const router = useRouter()
  const [enrolling, setEnrolling] = useState(false)

  const handleEnroll = async () => {
    try {
      setEnrolling(true)
      const response = await fetch(`/api/individual/modules/${module.id}/enroll`, {
        method: 'POST',
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to enroll')
      }

      toast.success(`Successfully enrolled in ${module.name}!`)
      window.location.reload()
      
    } catch (error: any) {
      console.error('Enrollment error:', error)
      toast.error(error.message || 'Failed to enroll in module')
    } finally {
      setEnrolling(false)
    }
  }

  const handleAccessModule = () => {
    if (module.lessons.length > 0) {
      const firstLesson = module.lessons[0]
      router.push(`/individual/courses/${courseId}/modules/${module.id}/lessons/${firstLesson.id}`)
    } else {
      router.push(`/individual/courses/${courseId}/modules/${module.id}`)
    }
  }

  // Calculate progress statistics
  const totalLessons = module.lessons.length
  const completedLessons = module.lessons.filter(lesson => lesson.status === 'completed').length
  const inProgressLessons = module.lessons.filter(lesson => lesson.status === 'in_progress').length
  
  const progressPercentage = enrollment?.progressPercentage || 0
  const moduleStatus = enrollment?.completedAt 
    ? "completed" 
    : enrollment?.startedAt 
      ? "in_progress" 
      : "not_started"

  // Get next lesson for continue learning
  const getNextLesson = () => {
    for (const lesson of module.lessons) {
      if (lesson.status !== 'completed') {
        return lesson
      }
    }
    return module.lessons[0] || null
  }

  const nextLesson = getNextLesson()

  return (
    <main className="min-h-screen bg-background">
      
      <div className="max-w-screen-xl mx-auto px-4 sm:px-6 py-6 md:py-8">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
          {/* Main content column */}
          <div className="lg:col-span-8 space-y-6">
            {/* Module info card */}
            <Card className="overflow-hidden border-none shadow-sm">
              <CardContent className="p-0">
                {/* Module status bar */}
                {moduleStatus !== "not_started" && (
                  <div className="bg-primary/5 dark:bg-primary/10 px-6 py-3 flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <BarChart className="h-4 w-4 text-primary" />
                      <span className="text-sm font-medium">
                        {moduleStatus === "completed" 
                          ? "Module completed" 
                          : `${progressPercentage}% complete`}
                      </span>
                    </div>
                    <Progress 
                      value={progressPercentage} 
                      className="w-24 h-2 bg-primary/10 [&>div]:bg-primary"
                    />
                  </div>
                )}
                
                <div className="p-6 bg-blue-50 dark:bg-blue-900/10">
                  {/* Module metadata */}
                  <div className="flex flex-wrap gap-4 mb-6">
                    <div className="flex items-center text-sm text-muted-foreground">
                      <Layout className="h-4 w-4 mr-2" />
                      <span>{totalLessons} {totalLessons === 1 ? 'lesson' : 'lessons'}</span>
                    </div>
                    
                    {module.estimatedDuration && (
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Clock className="h-4 w-4 mr-2" />
                        <span>
                          {Math.round(module.estimatedDuration / 60)} hours
                        </span>
                      </div>
                    )}
                    
                    <div className="flex items-center text-sm text-muted-foreground">
                      <Users className="h-4 w-4 mr-2" />
                      <span>For all learners</span>
                    </div>
                  </div>
                  
                  {/* Description */}
                  <div className="prose prose-xs max-w-none dark:prose-invert text-sm [&>p]:text-sm [&>ul]:text-sm [&>ol]:text-sm">
                    <RichTextDisplay 
                      content={module.description || "<p>No description available for this module.</p>"} 
                    />
                  </div>
                  
                  {/* Continue button - only if enrolled and has content */}
                  {isEnrolled && totalLessons > 0 && (
                    <div className="mt-6">
                      <Button 
                        size="lg"
                        className="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white"
                        onClick={handleAccessModule}
                      >
                        {moduleStatus === "completed" ? (
                          <>Review Module</>
                        ) : moduleStatus === "in_progress" ? (
                          <>Continue Learning</>
                        ) : (
                          <>Start Module</>
                        )}
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
            
            {/* Lessons section */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold">Module Content</h2>
                <div className="text-sm text-muted-foreground">
                  {completedLessons}/{totalLessons} completed
                </div>
              </div>
              
              {/* Empty state */}
              {totalLessons === 0 && (
                <Card className="border-dashed bg-transparent py-12">
                  <CardContent className="flex flex-col items-center text-center p-6">
                    <AlertCircle className="h-10 w-10 text-muted-foreground mb-3" />
                    <h3 className="text-lg font-medium mb-1">No lessons available</h3>
                    <p className="text-sm text-muted-foreground max-w-md">
                      This module doesn't have any content yet. Please check back later.
                    </p>
                  </CardContent>
                </Card>
              )}
              
              {/* Lesson list */}
              {totalLessons > 0 && (
                <div className="divide-y rounded-md border overflow-hidden">
                  {module.lessons.map((lesson, index) => {
                    const progress = { 
                      status: lesson.status || "not_started", 
                      progressPercentage: 0
                    };
                    
                    const isCompleted = progress.status === "completed";
                    const isInProgress = progress.status === "in_progress";
                    const isLocked = false;
                    const isQuiz = lesson.lessonType === 'quiz';
                    
                    return (
                      <div 
                        key={lesson.id} 
                        className={`bg-card hover:bg-accent/5 transition-colors ${
                          isLocked ? "opacity-60" : ""
                        }`}
                      >
                        <Link
                          href={isLocked ? "#" : `/individual/courses/${courseId}/modules/${module.id}/lessons/${lesson.id}`}
                          className={`block ${isLocked ? "cursor-not-allowed" : ""}`}
                        >
                          <div className="px-4 py-3 sm:px-6 sm:py-4">
                            <div className="flex items-center justify-between">
                              <div className="min-w-0 flex-1">
                                <div className="flex items-center">
                                  {/* Status indicator */}
                                  <div className={`w-6 h-6 rounded-full flex items-center justify-center mr-3 flex-shrink-0 ${
                                    isCompleted 
                                      ? "bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400" 
                                      : isInProgress 
                                        ? "bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400"
                                        : isLocked
                                          ? "bg-gray-100 text-gray-500 dark:bg-gray-800 dark:text-gray-400"
                                          : isQuiz
                                            ? "bg-purple-100 text-purple-600 dark:bg-purple-900/30 dark:text-purple-400"
                                            : "bg-primary/10 text-primary"
                                  }`}>
                                    {isCompleted ? (
                                      <CheckCircle className="h-3.5 w-3.5" />
                                    ) : isLocked ? (
                                      <Lock className="h-3.5 w-3.5" />
                                    ) : isQuiz ? (
                                      <FileQuestion className="h-3.5 w-3.5" />
                                    ) : (
                                      <Video className="h-3.5 w-3.5" />
                                    )}
                                  </div>
                                  
                                  <div className="min-w-0 flex-1">
                                    <h3 className="text-sm font-medium truncate flex items-center">
                                      {lesson.name}
                                      {isQuiz && (
                                        <Badge 
                                          variant="outline" 
                                          className="ml-2 bg-purple-50 text-purple-700 border-purple-200 text-xs"
                                        >
                                          Quiz
                                        </Badge>
                                      )}
                                    </h3>
                                    
                                    <div className="flex items-center mt-1">
                                      {lesson.estimatedDuration && (
                                        <span className="text-xs text-muted-foreground flex items-center">
                                          <Clock className="h-3 w-3 mr-1" />
                                          {lesson.estimatedDuration} min
                                        </span>
                                      )}
                                      
                                      {isInProgress && (
                                        <Badge 
                                          variant="secondary" 
                                          className="ml-2 text-xs px-1.5 h-5 rounded-sm"
                                        >
                                          In progress
                                        </Badge>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              </div>
                              
                              <ChevronRight className="h-5 w-5 text-muted-foreground/50" />
                            </div>
                            
                            {/* Progress bar - only for in-progress lessons */}
                            {isInProgress && (
                              <div className="mt-2">
                                <Progress 
                                  value={progress.progressPercentage} 
                                  className="h-1 [&>div]:bg-blue-500"
                                />
                              </div>
                            )}
                          </div>
                        </Link>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          </div>

          {/* Sidebar column */}
          <div className="lg:col-span-4">
            <div className="lg:sticky lg:top-24 space-y-6">
              {/* Module progress card */}
              <Card>
                <CardContent className="pt-6">
                  <h3 className="text-lg font-medium mb-4">Your Progress</h3>

                  <div className="space-y-3 mb-6">
                    <div className="flex justify-between text-sm">
                      <span>Completion</span>
                      <span className="font-medium">{progressPercentage}%</span>
                    </div>
                    <Progress
                      value={progressPercentage}
                      className="h-2 [&>div]:bg-primary"
                    />
                  </div>

                  <div className="grid grid-cols-3 gap-3 text-center">
                    <div className="bg-primary/5 dark:bg-primary/10 rounded-md p-3">
                      <div className="text-2xl font-bold">{totalLessons}</div>
                      <div className="text-xs text-muted-foreground">Total</div>
                    </div>
                    <div className="bg-green-50 dark:bg-green-900/20 rounded-md p-3">
                      <div className="text-2xl font-bold text-green-600 dark:text-green-400">{completedLessons}</div>
                      <div className="text-xs text-muted-foreground">Completed</div>
                    </div>
                    <div className="bg-blue-50 dark:bg-blue-900/20 rounded-md p-3">
                      <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{inProgressLessons}</div>
                      <div className="text-xs text-muted-foreground">In Progress</div>
                    </div>
                  </div>

                  {/* Action button based on status */}
                  {isEnrolled && totalLessons > 0 && (
                    <div className="mt-5">
                      <Button
                        variant={moduleStatus === "not_started" ? "default" : "secondary"}
                        className="w-full"
                        onClick={handleAccessModule}
                      >
                        <span className="flex items-center justify-center">
                          {moduleStatus === "completed" ? (
                            <>
                              <FileText className="h-4 w-4 mr-2" />
                              Review First Lesson
                            </>
                          ) : moduleStatus === "in_progress" ? (
                            <>
                              <PlayCircle className="h-4 w-4 mr-2" />
                              Continue Learning
                            </>
                          ) : (
                            <>
                              <Play className="h-4 w-4 mr-2" />
                              Start First Lesson
                            </>
                          )}
                        </span>
                      </Button>
                    </div>
                  )}

                  {/* Enrollment section for non-enrolled users */}
                  {!isEnrolled && (
                    <div className="mt-5">
                      {module.price && module.price > 0 ? (
                        <>
                          <div className="text-center mb-4">
                            <div className="text-2xl font-bold mb-2">
                              ${module.price}
                            </div>
                            <p className="text-sm text-muted-foreground">One-time payment</p>
                          </div>

                          <Button
                            className="w-full"
                            onClick={handleEnroll}
                            disabled={enrolling}
                          >
                            {enrolling ? 'Enrolling...' : 'Purchase Module'}
                          </Button>
                        </>
                      ) : (
                        <Button
                          className="w-full"
                          onClick={handleEnroll}
                          disabled={enrolling}
                        >
                          {enrolling ? 'Enrolling...' : 'Enroll for Free'}
                        </Button>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Additional card for help */}
              <Card>
                <CardContent className="pt-6">
                  <h3 className="text-lg font-medium mb-4">Need Help?</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    If you need assistance with this module or have questions, please reach out to our support team.
                  </p>
                  <Button variant="outline" className="w-full">Contact Support</Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </main>
  )
}
