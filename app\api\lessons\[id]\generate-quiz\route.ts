import { NextRequest, NextResponse } from 'next/server'
import { createAdminClient } from '@/lib/supabase'
import { requireAuth, createAuthErrorResponse } from '@/lib/auth-utils'

// AI Quiz Generation Service
class AIQuizGenerator {
  private async generateQuestions(
    lessonContent: string,
    learningObjectives: string,
    questionCount: number,
    questionTypes: string[],
    difficultyLevel: string = 'medium'
  ) {
    // This would integrate with your AI service (Together AI, OpenAI, etc.)
    // For now, I'll create a mock implementation
    
    const mockQuestions = []
    
    for (let i = 1; i <= questionCount; i++) {
      const questionType = questionTypes[Math.floor(Math.random() * questionTypes.length)]
      
      let question
      
      switch (questionType) {
        case 'multiple_choice':
          question = {
            question_number: i,
            question_text: `Based on the lesson content, which of the following statements is most accurate regarding the key concepts discussed?`,
            question_type: 'multiple_choice',
            question_context: lessonContent.substring(0, 200) + '...',
            answer_options: [
              { id: 'a', text: 'Option A - This represents the primary concept', is_correct: true },
              { id: 'b', text: 'Option B - This is a secondary consideration', is_correct: false },
              { id: 'c', text: 'Option C - This is not directly related', is_correct: false },
              { id: 'd', text: 'Option D - This contradicts the main idea', is_correct: false }
            ],
            correct_answer: 'a',
            explanation: 'Option A is correct because it aligns with the primary learning objective of understanding the core concepts presented in this lesson.',
            points: 10,
            difficulty_level: difficultyLevel,
            estimated_time: 60,
            ai_confidence_score: 0.85
          }
          break
          
        case 'true_false':
          question = {
            question_number: i,
            question_text: `True or False: The main concept discussed in this lesson is fundamental to understanding the broader topic.`,
            question_type: 'true_false',
            question_context: null,
            answer_options: [],
            correct_answer: 'true',
            explanation: 'This statement is true because the lesson focuses on foundational concepts that are essential for deeper understanding.',
            points: 5,
            difficulty_level: difficultyLevel,
            estimated_time: 30,
            ai_confidence_score: 0.90
          }
          break
          
        case 'short_answer':
          question = {
            question_number: i,
            question_text: `In one or two sentences, explain the key takeaway from this lesson.`,
            question_type: 'short_answer',
            question_context: null,
            answer_options: [],
            correct_answer: 'The key takeaway is understanding the fundamental principles and their practical applications.',
            explanation: 'A good answer should demonstrate understanding of the core concepts and their relevance.',
            points: 15,
            difficulty_level: difficultyLevel,
            estimated_time: 120,
            ai_confidence_score: 0.75
          }
          break
          
        case 'essay':
          question = {
            question_number: i,
            question_text: `Provide a detailed analysis of the main concepts covered in this lesson and explain how they relate to real-world applications.`,
            question_type: 'essay',
            question_context: learningObjectives,
            answer_options: [],
            correct_answer: 'A comprehensive answer should cover the main concepts, provide examples, and demonstrate critical thinking about practical applications.',
            explanation: 'This question assesses deep understanding and the ability to connect theoretical knowledge with practical scenarios.',
            points: 25,
            difficulty_level: difficultyLevel,
            estimated_time: 300,
            ai_confidence_score: 0.70
          }
          break
          
        case 'code_challenge':
          question = {
            question_number: i,
            question_text: `Write a code snippet that demonstrates the concept discussed in this lesson.`,
            question_type: 'code_challenge',
            question_context: `// Based on the lesson content, implement the following:\n// ${learningObjectives.substring(0, 100)}...`,
            answer_options: [],
            correct_answer: '// Sample solution\nfunction example() {\n  // Implementation here\n  return result;\n}',
            explanation: 'The solution should demonstrate understanding of the programming concepts covered in the lesson.',
            points: 20,
            difficulty_level: difficultyLevel,
            estimated_time: 240,
            ai_confidence_score: 0.80
          }
          break
          
        default:
          question = {
            question_number: i,
            question_text: `What is the most important concept covered in this lesson?`,
            question_type: 'multiple_choice',
            question_context: null,
            answer_options: [
              { id: 'a', text: 'Concept A', is_correct: true },
              { id: 'b', text: 'Concept B', is_correct: false },
              { id: 'c', text: 'Concept C', is_correct: false },
              { id: 'd', text: 'Concept D', is_correct: false }
            ],
            correct_answer: 'a',
            explanation: 'This is the primary focus of the lesson.',
            points: 10,
            difficulty_level: difficultyLevel,
            estimated_time: 60,
            ai_confidence_score: 0.85
          }
      }
      
      mockQuestions.push(question)
    }
    
    return mockQuestions
  }

  async generateQuizForUser(
    lessonId: string,
    userId: string,
    config: {
      question_count: number
      question_types: string[]
      learning_objectives: string
      difficulty_level?: string
    }
  ) {
    const supabase = createAdminClient()
    
    // Get lesson content for context
    const { data: lesson, error: lessonError } = await supabase
      .from('course_lessons')
      .select('name, description, content_data')
      .eq('id', lessonId)
      .single()
    
    if (lessonError || !lesson) {
      throw new Error('Lesson not found')
    }
    
    // Create lesson content context
    const lessonContent = `${lesson.name}\n${lesson.description || ''}\n${JSON.stringify(lesson.content_data || {})}`
    
    // Generate questions using AI
    const questions = await this.generateQuestions(
      lessonContent,
      config.learning_objectives,
      config.question_count,
      config.question_types,
      config.difficulty_level || 'medium'
    )
    
    // Create quiz generation record
    const { data: generation, error: generationError } = await supabase
      .from('lesson_quiz_generations')
      .insert({
        lesson_id: lessonId,
        user_id: userId,
        generation_prompt: `Generate ${config.question_count} questions of types: ${config.question_types.join(', ')}`,
        ai_model_used: 'mock-ai-v1',
        question_count: config.question_count,
        difficulty_distribution: { medium: config.question_count },
        generation_metadata: {
          learning_objectives: config.learning_objectives,
          question_types: config.question_types
        },
        status: 'generated'
      })
      .select()
      .single()
    
    if (generationError) {
      throw new Error('Failed to create quiz generation record')
    }
    
    // Store generated questions
    const questionsToInsert = questions.map(q => ({
      generation_id: generation.id,
      question_number: q.question_number,
      question_text: q.question_text,
      question_type: q.question_type,
      question_context: q.question_context,
      answer_options: q.answer_options,
      correct_answer: q.correct_answer,
      explanation: q.explanation,
      points: q.points,
      difficulty_level: q.difficulty_level,
      estimated_time: q.estimated_time,
      ai_confidence_score: q.ai_confidence_score
    }))
    
    const { error: questionsError } = await supabase
      .from('lesson_quiz_user_questions')
      .insert(questionsToInsert)
    
    if (questionsError) {
      throw new Error('Failed to store generated questions')
    }
    
    return {
      generation_id: generation.id,
      questions: questions,
      metadata: {
        total_questions: config.question_count,
        question_types: config.question_types,
        estimated_time: questions.reduce((sum, q) => sum + q.estimated_time, 0),
        average_confidence: questions.reduce((sum, q) => sum + q.ai_confidence_score, 0) / questions.length
      }
    }
  }
}

export async function POST(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const authResult = await requireAuth()
    if (!authResult.user) {
      return createAuthErrorResponse(authResult)
    }

    const lessonId = params.id
    const body = await req.json()
    
    const {
      question_count = 10,
      question_types = ['multiple_choice'],
      learning_objectives = '',
      difficulty_level = 'medium'
    } = body

    // Validate input
    if (!lessonId) {
      return NextResponse.json(
        { error: 'Lesson ID is required' },
        { status: 400 }
      )
    }

    if (question_count < 1 || question_count > 50) {
      return NextResponse.json(
        { error: 'Question count must be between 1 and 50' },
        { status: 400 }
      )
    }

    const validQuestionTypes = ['multiple_choice', 'true_false', 'short_answer', 'essay', 'code_challenge']
    const invalidTypes = question_types.filter((type: string) => !validQuestionTypes.includes(type))
    
    if (invalidTypes.length > 0) {
      return NextResponse.json(
        { error: `Invalid question types: ${invalidTypes.join(', ')}` },
        { status: 400 }
      )
    }

    // Check if user already has a generated quiz for this lesson
    const supabase = createAdminClient()
    const { data: existingGeneration } = await supabase
      .from('lesson_quiz_generations')
      .select('id, status')
      .eq('lesson_id', lessonId)
      .eq('user_id', authResult.user.id)
      .single()

    if (existingGeneration && existingGeneration.status === 'generated') {
      return NextResponse.json(
        { error: 'Quiz already generated for this lesson. Complete the existing quiz first.' },
        { status: 409 }
      )
    }

    // Generate new quiz
    const generator = new AIQuizGenerator()
    const result = await generator.generateQuizForUser(lessonId, authResult.user.id, {
      question_count,
      question_types,
      learning_objectives,
      difficulty_level
    })

    return NextResponse.json({
      success: true,
      message: 'Quiz generated successfully',
      data: result
    })

  } catch (error: any) {
    console.error('Quiz generation error:', error)
    return NextResponse.json(
      { error: error.message || 'Failed to generate quiz' },
      { status: 500 }
    )
  }
}

// GET endpoint to retrieve generated quiz for user
export async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const authResult = await requireAuth()
    if (!authResult.user) {
      return createAuthErrorResponse(authResult)
    }

    const lessonId = params.id
    const supabase = createAdminClient()

    // Get the user's quiz generation for this lesson
    const { data: generation, error: generationError } = await supabase
      .from('lesson_quiz_generations')
      .select('*')
      .eq('lesson_id', lessonId)
      .eq('user_id', authResult.user.id)
      .single()

    if (generationError || !generation) {
      return NextResponse.json(
        { error: 'No quiz found for this lesson. Generate a quiz first.' },
        { status: 404 }
      )
    }

    // Get the generated questions
    const { data: questions, error: questionsError } = await supabase
      .from('lesson_quiz_user_questions')
      .select('*')
      .eq('generation_id', generation.id)
      .order('question_number')

    if (questionsError) {
      return NextResponse.json(
        { error: 'Failed to fetch quiz questions' },
        { status: 500 }
      )
    }

    // Get lesson info
    const { data: lesson, error: lessonError } = await supabase
      .from('course_lessons')
      .select('name, passing_score, attempts_allowed, time_limit, content_data')
      .eq('id', lessonId)
      .single()

    if (lessonError || !lesson) {
      return NextResponse.json(
        { error: 'Lesson not found' },
        { status: 404 }
      )
    }

    // Format quiz config
    const quizConfig = {
      title: `${lesson.name} - Quiz`,
      instructions: generation.generation_metadata?.instructions || 'Answer all questions to the best of your ability.',
      time_limit: lesson.time_limit,
      passing_score: lesson.passing_score || 70,
      attempts_allowed: lesson.attempts_allowed || 3,
      questions: questions.map(q => ({
        id: q.id,
        question_number: q.question_number,
        question_text: q.question_text,
        question_type: q.question_type,
        question_context: q.question_context,
        answer_options: q.answer_options || [],
        correct_answer: q.correct_answer,
        explanation: q.explanation,
        points: q.points,
        estimated_time: q.estimated_time
      }))
    }

    return NextResponse.json({
      success: true,
      data: {
        generation_id: generation.id,
        quiz_config: quizConfig,
        metadata: {
          generated_at: generation.generation_timestamp,
          question_count: generation.question_count,
          status: generation.status
        }
      }
    })

  } catch (error: any) {
    console.error('Quiz fetch error:', error)
    return NextResponse.json(
      { error: error.message || 'Failed to fetch quiz' },
      { status: 500 }
    )
  }
}
