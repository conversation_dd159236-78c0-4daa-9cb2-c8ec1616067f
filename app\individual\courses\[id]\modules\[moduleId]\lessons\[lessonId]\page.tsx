import { createServerComponentClient } from "@supabase/auth-helpers-nextjs"
import { createClient } from '@supabase/supabase-js'
import { cookies } from "next/headers"
import { redirect } from "next/navigation"
import { Database } from "@/types/supabase"
import { ArrowLeft, BookOpen, Clock, CheckCircle, FileText } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import Link from "next/link"
import { RichTextDisplay } from "@/components/ui/rich-text-display"
import { Separator } from "@/components/ui/separator"
import { PageTitle } from "@/components/page-title"

// Create admin client for operations that need to bypass RLS
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || ""
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || ""
const adminClient = createClient<Database>(supabaseUrl, supabaseServiceKey)

export default async function CourseLessonDetailsPage({
  params,
}: {
  params: { id: string; moduleId: string; lessonId: string }
}) {
  const { id: courseId, moduleId, lessonId } = params;
  const cookiesStore = cookies();
  const supabase = createServerComponentClient<Database>({ cookies: () => cookiesStore });
  
  // Check if user is authenticated
  const { data: { session } } = await supabase.auth.getSession()
  if (!session) {
    redirect("/login")
  }
  
  // Fetch the course
  const { data: course, error: courseError } = await adminClient
    .from("courses")
    .select("*")
    .eq("id", courseId)
    .single()
  
  if (courseError || !course) {
    redirect("/individual/courses-marketplace")
  }
  
  // Fetch the module
  const { data: module, error: moduleError } = await adminClient
    .from("course_modules")
    .select("*")
    .eq("id", moduleId)
    .eq("course_id", courseId)
    .single()
  
  if (moduleError || !module) {
    redirect(`/individual/courses/${courseId}`)
  }
  
  // Fetch the lesson
  const { data: lesson, error: lessonError } = await adminClient
    .from("course_lessons")
    .select("*")
    .eq("id", lessonId)
    .eq("module_id", moduleId)
    .single()
  
  if (lessonError || !lesson) {
    redirect(`/individual/courses/${courseId}/modules/${moduleId}`)
  }
  
  // Fetch all lessons for this module for navigation
  const { data: allLessons, error: allLessonsError } = await adminClient
    .from("course_lessons")
    .select("id, name, description, estimated_duration, sequence_order, lesson_type")
    .eq("module_id", moduleId)
    .order("sequence_order", { ascending: true });
    
  if (allLessonsError) {
    console.error("Error fetching all lessons:", allLessonsError);
  }
  
  // Check if user is enrolled
  const { data: enrollment } = await adminClient
    .from("user_course_enrollments")
    .select("*")
    .eq("course_id", courseId)
    .eq("user_id", session.user.id)
    .single()
  
  const isEnrolled = !!enrollment
  
  if (!isEnrolled) {
    redirect(`/individual/courses/${courseId}`)
  }
  
  return (
    <div className="flex h-full bg-background">
      {/* Sidebar navigation */}
      <div className="w-80 border-r bg-card flex flex-col">
        {/* Header */}
        <div className="p-4 border-b">
          <h2 className="font-semibold text-primary">{module.name}</h2>
          <p className="text-xs text-muted-foreground mt-0.5">
            {allLessons?.length || 0} lessons
          </p>
        </div>
        
        {/* Lessons list */}
        <div className="flex-1 overflow-y-auto p-2">
          {allLessons?.map((lessonItem, index) => {
            const isCurrent = lessonItem.id === lessonId;
            const isCompleted = false; // TODO: Get actual progress
            const isInProgress = false; // TODO: Get actual progress
            
            return (
              <Link 
                key={lessonItem.id} 
                href={`/individual/courses/${courseId}/modules/${moduleId}/lessons/${lessonItem.id}`}
                className={`flex items-start gap-3 p-2.5 rounded-lg transition-all mb-2 ${
                  isCurrent 
                    ? "bg-primary/10 text-primary ring-1 ring-primary/20" 
                    : "hover:bg-muted/80"
                }`}
              >
                <div className={`flex items-center justify-center rounded-md w-7 h-7 shrink-0 ${
                  isCompleted ? "text-green-600 bg-green-100" :
                  isInProgress ? "text-blue-600 bg-blue-100" :
                  isCurrent ? "text-primary bg-primary/10" :
                  "text-muted-foreground bg-muted"
                }`}>
                  {isCompleted ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <span className="text-xs font-medium">{index + 1}</span>
                  )}
                </div>
                
                <div className="flex-1 min-w-0">
                  <h3 className="text-sm font-medium truncate">{lessonItem.name}</h3>
                  {lessonItem.estimated_duration && (
                    <div className="flex items-center mt-1 text-xs text-muted-foreground">
                      <Clock className="h-3 w-3 mr-1" />
                      {lessonItem.estimated_duration} min
                    </div>
                  )}
                </div>
              </Link>
            );
          })}
        </div>
      </div>
      
      {/* Page title updater */}
      <PageTitle title={lesson.name} />
      
      {/* Main content area */}
      <main className="flex-1 overflow-y-auto">
        <div className="max-w-5xl mx-auto px-4 md:px-6 py-6">
          {/* Lesson title and navigation */}
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-2xl font-bold tracking-tight">{lesson.name}</h1>
            
            <div className="flex items-center gap-2">
              <Button 
                variant="outline" 
                size="sm" 
                asChild
              >
                <Link href={`/individual/courses/${courseId}/modules/${moduleId}`}>
                  <ArrowLeft className="h-4 w-4 mr-1.5" />
                  Back to Module
                </Link>
              </Button>
            </div>
          </div>
          
          <div className="space-y-8">
            {/* Video section */}
            {lesson.content_url ? (
              <div className="rounded-xl overflow-hidden shadow-lg">
                <video 
                  src={lesson.content_url} 
                  controls
                  className="w-full aspect-video bg-black"
                >
                  Your browser does not support the video tag.
                </video>
              </div>
            ) : (
              <Card className="border shadow-sm overflow-hidden bg-card">
                <CardContent className="p-6 md:p-8 flex flex-col items-center justify-center text-center">
                  <FileText className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium">No Video Available</h3>
                  <p className="text-muted-foreground mt-2">This lesson doesn't include a video component.</p>
                </CardContent>
              </Card>
            )}
            
            {/* Lesson content */}
            <Card className="border shadow-sm overflow-hidden bg-card">
              <CardContent className="p-6 md:p-8">
                <div className="prose prose-gray prose-sm dark:prose-invert max-w-none">
                  <RichTextDisplay 
                    content={lesson.description || "<p>No description available for this lesson.</p>"} 
                  />
                </div>
              </CardContent>
            </Card>
            
            {/* Footer */}
            <Separator className="my-6" />
            
            <div className="flex justify-center mt-6">
              <Button size="lg">
                Mark as Complete
              </Button>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
