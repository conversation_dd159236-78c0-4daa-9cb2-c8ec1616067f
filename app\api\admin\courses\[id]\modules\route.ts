import { NextRequest, NextResponse } from 'next/server'
import { requirePlatformAdmin } from '@/lib/auth/middleware'
import { createAdminClient } from '@/lib/supabase-admin'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const authResult = await requirePlatformAdmin()
    if (!authResult.user) {
      return NextResponse.json(
        { error: authResult.error || 'Authentication required' },
        { status: authResult.status || 401 }
      )
    }

    const { id: courseId } = await params
    const supabase = createAdminClient()

    // Fetch modules with their lessons
    const { data: modules, error: modulesError } = await supabase
      .from('course_modules')
      .select(`
        *,
        lessons:course_lessons(*)
      `)
      .eq('course_id', courseId)
      .order('sequence_order', { ascending: true })

    if (modulesError) {
      console.error('Error fetching modules:', modulesError)
      return NextResponse.json(
        { error: 'Failed to fetch course modules' },
        { status: 500 }
      )
    }

    // Sort lessons within each module
    const modulesWithSortedLessons = modules?.map(module => ({
      ...module,
      lessons: module.lessons?.sort((a: any, b: any) => a.sequence_order - b.sequence_order) || []
    })) || []

    return NextResponse.json({
      modules: modulesWithSortedLessons
    })

  } catch (error) {
    console.error('Error in GET /api/admin/courses/[id]/modules:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const authResult = await requirePlatformAdmin()
    if (!authResult.user) {
      return NextResponse.json(
        { error: authResult.error || 'Authentication required' },
        { status: authResult.status || 401 }
      )
    }

    const courseId = params.id
    const body = await request.json()

    const {
      name,
      description,
      module_type = 'theory',
      difficulty_level = 'beginner',
      estimated_duration,
      is_standalone = false,
      single_price,
      status = 'draft',
      sequence_order,
      learning_objectives = [],
      required_resources = [],
      module_prerequisites = []
    } = body

    // Validation
    if (!name?.trim()) {
      return NextResponse.json(
        { error: 'Module name is required' },
        { status: 400 }
      )
    }

    const supabase = createAdminClient()

    // If no sequence_order provided, get the next available order
    let finalSequenceOrder = sequence_order
    if (!finalSequenceOrder) {
      const { data: lastModule } = await supabase
        .from('course_modules')
        .select('sequence_order')
        .eq('course_id', courseId)
        .order('sequence_order', { ascending: false })
        .limit(1)
        .single()

      finalSequenceOrder = (lastModule?.sequence_order || 0) + 1
    }

    // Create the module
    const { data: module, error: moduleError } = await supabase
      .from('course_modules')
      .insert({
        course_id: courseId,
        name: name.trim(),
        description: description?.trim() || '',
        module_type,
        difficulty_level,
        estimated_duration,
        is_standalone,
        single_price,
        status,
        sequence_order: finalSequenceOrder,
        learning_objectives,
        required_resources,
        module_prerequisites
      })
      .select()
      .single()

    if (moduleError) {
      console.error('Error creating module:', moduleError)
      return NextResponse.json(
        { error: 'Failed to create module' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      module,
      message: 'Module created successfully'
    })

  } catch (error) {
    console.error('Error in POST /api/admin/courses/[id]/modules:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
