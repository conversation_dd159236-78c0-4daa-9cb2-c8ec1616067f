-- =============================================================================
-- LESSON QUIZ ENHANCEMENT MIGRATION
-- Adds support for lesson-level quizzes with AI-generated questions
-- =============================================================================

-- =============================================================================
-- STEP 1: UPDATE LESSON TYPES
-- =============================================================================

-- Update lesson_type enum to include presentation
ALTER TABLE course_lessons 
DROP CONSTRAINT IF EXISTS course_lessons_lesson_type_check;

ALTER TABLE course_lessons 
ADD CONSTRAINT course_lessons_lesson_type_check 
CHECK (lesson_type IN ('video', 'text', 'presentation', 'quiz', 'assignment', 'interactive'));

-- =============================================================================
-- STEP 2: ADD CONTENT STORAGE FIELDS
-- =============================================================================

-- Add file storage fields for uploaded content
ALTER TABLE course_lessons 
ADD COLUMN IF NOT EXISTS content_file_url TEXT, -- URL to uploaded file in course-media bucket
ADD COLUMN IF NOT EXISTS content_file_name TEXT, -- Original filename
ADD COLUMN IF NOT EXISTS content_file_size INTEGER, -- File size in bytes
ADD COLUMN IF NOT EXISTS content_file_type TEXT, -- MIME type
ADD COLUMN IF NOT EXISTS content_source TEXT DEFAULT 'url' CHECK (content_source IN ('url', 'upload')), -- Source type
ADD COLUMN IF NOT EXISTS slide_count INTEGER, -- For presentation lessons
ADD COLUMN IF NOT EXISTS reading_time INTEGER; -- For text lessons (in minutes)

-- =============================================================================
-- STEP 3: CREATE LESSON QUIZ QUESTIONS TABLE
-- =============================================================================

-- Table to store quiz questions for individual lessons
CREATE TABLE IF NOT EXISTS course_lesson_quiz_questions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  lesson_id UUID NOT NULL REFERENCES course_lessons(id) ON DELETE CASCADE,
  question_text TEXT NOT NULL,
  question_type TEXT NOT NULL CHECK (question_type IN ('multiple_choice', 'true_false', 'short_answer', 'essay', 'code_challenge')),
  question_context TEXT, -- Additional context or code snippets
  answer_options JSONB DEFAULT '[]', -- Array of answer options for multiple choice
  correct_answer TEXT NOT NULL,
  explanation TEXT, -- Explanation of the correct answer
  points INTEGER DEFAULT 1,
  difficulty_level TEXT DEFAULT 'medium' CHECK (difficulty_level IN ('easy', 'medium', 'hard')),
  sequence_order INTEGER NOT NULL,
  time_limit INTEGER, -- Time limit in seconds for this question
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Ensure unique question order within each lesson
  UNIQUE(lesson_id, sequence_order)
);

-- =============================================================================
-- STEP 4: CREATE AI QUIZ GENERATION TRACKING
-- =============================================================================

-- Table to track AI-generated quiz instances for users
CREATE TABLE IF NOT EXISTS lesson_quiz_generations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  lesson_id UUID NOT NULL REFERENCES course_lessons(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  generation_prompt TEXT,
  ai_model_used TEXT DEFAULT 'together-ai',
  generation_timestamp TIMESTAMPTZ DEFAULT NOW(),
  question_count INTEGER NOT NULL,
  difficulty_distribution JSONB DEFAULT '{}', -- {"easy": 2, "medium": 3, "hard": 1}
  generation_metadata JSONB DEFAULT '{}', -- Additional AI generation data
  status TEXT DEFAULT 'generated' CHECK (status IN ('generated', 'completed', 'abandoned')),
  
  -- Ensure one generation per user per lesson (for now)
  UNIQUE(lesson_id, user_id)
);

-- =============================================================================
-- STEP 5: CREATE GENERATED QUIZ QUESTIONS TABLE
-- =============================================================================

-- Table to store the actual generated questions for each user
CREATE TABLE IF NOT EXISTS lesson_quiz_user_questions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  generation_id UUID NOT NULL REFERENCES lesson_quiz_generations(id) ON DELETE CASCADE,
  question_number INTEGER NOT NULL,
  question_text TEXT NOT NULL,
  question_type TEXT NOT NULL CHECK (question_type IN ('multiple_choice', 'true_false', 'short_answer', 'essay', 'code_challenge')),
  question_context TEXT,
  answer_options JSONB DEFAULT '[]',
  correct_answer TEXT NOT NULL,
  explanation TEXT,
  points INTEGER DEFAULT 1,
  difficulty_level TEXT DEFAULT 'medium' CHECK (difficulty_level IN ('easy', 'medium', 'hard')),
  estimated_time INTEGER, -- Estimated time to answer in seconds
  ai_confidence_score DECIMAL(3,2), -- AI confidence in question quality (0.00-1.00)
  
  -- Ensure unique question numbers within each generation
  UNIQUE(generation_id, question_number)
);

-- =============================================================================
-- STEP 6: CREATE USER QUIZ ATTEMPTS TABLE
-- =============================================================================

-- Table to track user attempts at lesson quizzes
CREATE TABLE IF NOT EXISTS lesson_quiz_attempts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  generation_id UUID NOT NULL REFERENCES lesson_quiz_generations(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  lesson_id UUID NOT NULL REFERENCES course_lessons(id) ON DELETE CASCADE,
  attempt_number INTEGER NOT NULL DEFAULT 1,
  started_at TIMESTAMPTZ DEFAULT NOW(),
  completed_at TIMESTAMPTZ,
  status TEXT DEFAULT 'in_progress' CHECK (status IN ('in_progress', 'completed', 'abandoned', 'expired')),
  score DECIMAL(5,2), -- Final score as percentage
  passed BOOLEAN,
  time_spent INTEGER, -- Total time spent in seconds
  answers_data JSONB DEFAULT '{}', -- User's answers
  
  -- Track attempt numbers per user per lesson
  UNIQUE(user_id, lesson_id, attempt_number)
);

-- =============================================================================
-- STEP 7: CREATE INDEXES
-- =============================================================================

-- Lesson quiz questions indexes
CREATE INDEX IF NOT EXISTS idx_lesson_quiz_questions_lesson_id ON course_lesson_quiz_questions(lesson_id);
CREATE INDEX IF NOT EXISTS idx_lesson_quiz_questions_sequence ON course_lesson_quiz_questions(lesson_id, sequence_order);

-- Quiz generation indexes
CREATE INDEX IF NOT EXISTS idx_lesson_quiz_generations_lesson_id ON lesson_quiz_generations(lesson_id);
CREATE INDEX IF NOT EXISTS idx_lesson_quiz_generations_user_id ON lesson_quiz_generations(user_id);
CREATE INDEX IF NOT EXISTS idx_lesson_quiz_generations_status ON lesson_quiz_generations(status);

-- User questions indexes
CREATE INDEX IF NOT EXISTS idx_lesson_quiz_user_questions_generation_id ON lesson_quiz_user_questions(generation_id);
CREATE INDEX IF NOT EXISTS idx_lesson_quiz_user_questions_number ON lesson_quiz_user_questions(generation_id, question_number);

-- Quiz attempts indexes
CREATE INDEX IF NOT EXISTS idx_lesson_quiz_attempts_user_lesson ON lesson_quiz_attempts(user_id, lesson_id);
CREATE INDEX IF NOT EXISTS idx_lesson_quiz_attempts_generation_id ON lesson_quiz_attempts(generation_id);
CREATE INDEX IF NOT EXISTS idx_lesson_quiz_attempts_status ON lesson_quiz_attempts(status);

-- =============================================================================
-- STEP 8: CREATE TRIGGERS
-- =============================================================================

-- Update timestamp triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to new tables
CREATE TRIGGER update_lesson_quiz_questions_updated_at BEFORE UPDATE ON course_lesson_quiz_questions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =============================================================================
-- STEP 9: ADD COMMENTS
-- =============================================================================

COMMENT ON TABLE course_lesson_quiz_questions IS 'Static quiz questions defined for lessons (template questions)';
COMMENT ON TABLE lesson_quiz_generations IS 'Tracks AI-generated quiz instances for users';
COMMENT ON TABLE lesson_quiz_user_questions IS 'Stores the actual AI-generated questions for each user';
COMMENT ON TABLE lesson_quiz_attempts IS 'Tracks user attempts at lesson quizzes';

COMMENT ON COLUMN course_lessons.content_source IS 'Whether content is from URL or uploaded file';
COMMENT ON COLUMN course_lessons.content_file_url IS 'URL to uploaded file in course-media storage bucket';
COMMENT ON COLUMN course_lessons.slide_count IS 'Number of slides for presentation lessons';

-- =============================================================================
-- MIGRATION COMPLETE
-- =============================================================================

SELECT 'Lesson quiz enhancement migration completed successfully!' as message;
