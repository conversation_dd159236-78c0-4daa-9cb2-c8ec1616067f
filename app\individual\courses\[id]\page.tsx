import { createServerComponentClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { notFound } from 'next/navigation'
import { Database } from '@/types/supabase'
import CourseDetailsPage from '@/components/course-details-page'

export default async function CourseDetails({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params
  const supabase = createServerComponentClient<Database>({ cookies })

  // Check if user is authenticated
  const { data: { session } } = await supabase.auth.getSession()
  if (!session) {
    return notFound()
  }

  // Fetch the specific course
  const { data: course, error } = await supabase
    .from("courses")
    .select(`
      *,
      instructor:users!courses_instructor_id_fkey(
        id,
        full_name,
        email
      ),
      course_modules(
        id,
        name,
        description,
        sequence_order,
        estimated_duration,
        status,
        learning_objectives,
        course_lessons(
          id,
          name,
          description,
          lesson_type,
          sequence_order,
          estimated_duration,
          is_mandatory,
          status
        )
      )
    `)
    .eq("id", id)
    .eq("status", "published")
    .eq("is_standalone", true)
    .single()

  if (error || !course) {
    notFound()
  }

  // Check if user is enrolled
  const { data: enrollment } = await supabase
    .from('user_course_enrollments')
    .select(`
      id,
      enrolled_at,
      started_at,
      completed_at,
      progress_percentage,
      current_module_id,
      current_lesson_id
    `)
    .eq('user_id', session.user.id)
    .eq('course_id', id)
    .single()

  // Format course data for the component
  const formattedCourse = {
    id: course.id,
    name: course.name,
    description: course.description || '',
    instructor: course.instructor?.full_name || 'Unknown Instructor',
    instructorEmail: course.instructor?.email || '',
    level: course.level?.charAt(0).toUpperCase() + course.level?.slice(1) || 'Beginner',
    estimatedDuration: course.estimated_duration || 0,
    price: course.price || 0,
    coverImageUrl: course.cover_image_url || '/api/placeholder/400/300',
    previewVideoUrl: course.preview_video_url || null,
    tags: course.tags || [],
    learningObjectives: course.learning_objectives || [],
    targetAudience: course.target_audience || '',
    instructorBio: course.instructor_bio || '',
    enrollmentCount: course.enrollment_count || 0,
    averageRating: course.average_rating || 0,
    completionRate: course.completion_rate || 0,
    certificationAvailable: course.certification_available || false,
    language: course.language || 'English',
    requiredSoftware: course.required_software || [],
    hardwareRequirements: course.hardware_requirements || '',
    accessibilityFeatures: course.accessibility_features || [],
    
    // Module and lesson structure
    modules: course.course_modules?.map(module => ({
      id: module.id,
      name: module.name,
      description: module.description || '',
      sequenceOrder: module.sequence_order,
      estimatedDuration: module.estimated_duration || 0,
      status: module.status,
      learningObjectives: module.learning_objectives || [],
      lessons: module.course_lessons?.map(lesson => ({
        id: lesson.id,
        name: lesson.name,
        description: lesson.description || '',
        lessonType: lesson.lesson_type,
        sequenceOrder: lesson.sequence_order,
        estimatedDuration: lesson.estimated_duration || 0,
        isMandatory: lesson.is_mandatory,
        status: lesson.status
      })).sort((a, b) => a.sequenceOrder - b.sequenceOrder) || []
    })).sort((a, b) => a.sequenceOrder - b.sequenceOrder) || [],

    // Enrollment information
    isEnrolled: !!enrollment,
    enrollment: enrollment ? {
      id: enrollment.id,
      enrolledAt: enrollment.enrolled_at,
      startedAt: enrollment.started_at,
      completedAt: enrollment.completed_at,
      progressPercentage: enrollment.progress_percentage || 0,
      currentModuleId: enrollment.current_module_id,
      currentLessonId: enrollment.current_lesson_id
    } : null
  }

  return <CourseDetailsPage course={formattedCourse} />
}
