import { NextRequest, NextResponse } from 'next/server'
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { Database } from '@/types/supabase'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = createServerComponentClient<Database>({ cookies })
    
    // Check authentication
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id: courseId } = await params

    // Check if user is enrolled in this course
    const { data: enrollment, error } = await supabase
      .from('user_course_enrollments')
      .select(`
        id,
        enrolled_at,
        started_at,
        completed_at,
        progress_percentage,
        current_module_id,
        current_lesson_id,
        course:courses(
          id,
          name,
          slug,
          course_modules(
            id,
            name,
            sequence_order,
            course_lessons(
              id,
              name,
              sequence_order
            )
          )
        )
      `)
      .eq('user_id', session.user.id)
      .eq('course_id', courseId)
      .single()

    if (error && error.code !== 'PGRST116') { // PGRST116 is "not found" error
      console.error('Error checking enrollment:', error)
      return NextResponse.json(
        { error: 'Failed to check enrollment status' },
        { status: 500 }
      )
    }

    const isEnrolled = !!enrollment
    let nextUrl = null

    if (isEnrolled && enrollment.course) {
      // Find the first module and lesson to navigate to
      const firstModule = enrollment.course.course_modules
        ?.sort((a, b) => a.sequence_order - b.sequence_order)[0]
      
      if (firstModule) {
        const firstLesson = firstModule.course_lessons
          ?.sort((a, b) => a.sequence_order - b.sequence_order)[0]
        
        if (firstLesson) {
          nextUrl = `/individual/courses/${courseId}/modules/${firstModule.id}/lessons/${firstLesson.id}`
        } else {
          nextUrl = `/individual/courses/${courseId}/modules/${firstModule.id}`
        }
      } else {
        nextUrl = '/individual/courses-marketplace'
      }
    }

    return NextResponse.json({
      isEnrolled,
      enrollment: enrollment ? {
        id: enrollment.id,
        enrolledAt: enrollment.enrolled_at,
        startedAt: enrollment.started_at,
        completedAt: enrollment.completed_at,
        progressPercentage: enrollment.progress_percentage,
        currentModuleId: enrollment.current_module_id,
        currentLessonId: enrollment.current_lesson_id
      } : null,
      nextUrl,
      course: enrollment?.course ? {
        id: enrollment.course.id,
        name: enrollment.course.name,
        slug: enrollment.course.slug
      } : null
    })

  } catch (error) {
    console.error('Error in enrollment status API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
