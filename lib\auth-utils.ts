import { SupabaseClient } from '@supabase/supabase-js';
import type { Database } from '@/types/database.types';

/**
 * Check if user is a platform admin using secure function
 * @param supabase Supabase client
 * @param userId User ID to check
 */
export async function isPlatformAdmin(
  supabase: SupabaseClient<Database>,
  userId: string
): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .rpc('is_platform_admin', { user_id_param: userId });

    if (error) {
      console.error('Error checking platform admin status:', error);
      return false;
    }

    return data === true;
  } catch (err) {
    console.error('Error in isPlatformAdmin:', err);
    return false;
  }
}

/**
 * Check if user is a member of an organization using secure function
 * @param supabase Supabase client
 * @param userId User ID to check
 * @param organizationId Organization ID to check
 */
export async function isOrganizationMember(
  supabase: SupabaseClient<Database>,
  userId: string,
  organizationId: string
): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .rpc('is_organization_member', { 
        user_id_param: userId,
        org_id_param: organizationId 
      });

    if (error) {
      console.error('Error checking organization membership:', error);
      return false;
    }

    return data === true;
  } catch (err) {
    console.error('Error in isOrganizationMember:', err);
    return false;
  }
}

/**
 * Get user's organization memberships using secure function
 * @param supabase Supabase client
 * @param userId User ID to check
 */
export async function getUserOrganizations(
  supabase: SupabaseClient<Database>,
  userId: string
) {
  try {
    const { data, error } = await supabase
      .rpc('get_user_organizations', { user_id_param: userId });

    if (error) {
      // Don't log as error if user simply has no organization memberships
      if (error.message?.includes('no rows') || error.code === 'PGRST116') {
        return {
          organizations: [],
          error: null
        };
      }

      console.error('Error getting user organizations:', error);
      return {
        organizations: [],
        error: error
      };
    }

    return {
      organizations: data || [],
      error: null
    };
  } catch (err) {
    console.log('No organization memberships found for user (this is normal for individual users)');
    return {
      organizations: [],
      error: null
    };
  }
}

/**
 * Create or update user context for context switching
 * @param supabase Supabase client
 * @param userId User ID
 * @param context Context type ('individual' | 'organization')
 * @param organizationId Organization ID (required if context is 'organization')
 */
export async function updateUserContext(
  supabase: SupabaseClient<Database>,
  userId: string,
  context: 'individual' | 'organization',
  organizationId?: string
) {
  try {
    // Validate that organizationId is provided for organization context
    if (context === 'organization' && !organizationId) {
      throw new Error('Organization ID is required for organization context');
    }

    // Check if user context already exists
    const { data: existingContext, error: fetchError } = await supabase
      .from('user_contexts')
      .select('id')
      .eq('user_id', userId)
      .single();

    if (fetchError && fetchError.code !== 'PGRST116') {
      throw fetchError;
    }

    const contextData = {
      user_id: userId,
      active_context: context,
      active_organization_id: context === 'organization' ? organizationId : null,
      updated_at: new Date().toISOString()
    };

    if (existingContext) {
      // Update existing context
      const { data, error } = await supabase
        .from('user_contexts')
        .update(contextData)
        .eq('user_id', userId)
        .select()
        .single();

      if (error) throw error;
      return { data, error: null };
    } else {
      // Create new context
      const { data, error } = await supabase
        .from('user_contexts')
        .insert(contextData)
        .select()
        .single();

      if (error) throw error;
      return { data, error: null };
    }
  } catch (err) {
    console.error('Error updating user context:', err);
    return {
      data: null,
      error: err instanceof Error ? err : new Error('Unknown error updating user context')
    };
  }
}

/**
 * Get user's current context
 * @param supabase Supabase client
 * @param userId User ID
 */
export async function getUserContext(
  supabase: SupabaseClient<Database>,
  userId: string
) {
  try {
    const { data, error } = await supabase
      .from('user_contexts')
      .select('active_context, active_organization_id')
      .eq('user_id', userId)
      .single();

    if (error && error.code !== 'PGRST116') {
      throw error;
    }

    return {
      context: data || null,
      error: null
    };
  } catch (err) {
    console.error('Error getting user context:', err);
    return {
      context: null,
      error: err instanceof Error ? err : new Error('Unknown error getting user context')
    };
  }
}

/**
 * Check if user has specific role in organization
 * @param supabase Supabase client
 * @param userId User ID
 * @param organizationId Organization ID
 * @param requiredRole Required role ('owner' | 'admin' | 'manager' | 'member' | 'viewer')
 */
export async function hasOrganizationRole(
  supabase: SupabaseClient<Database>,
  userId: string,
  organizationId: string,
  requiredRole: 'owner' | 'admin' | 'manager' | 'member' | 'viewer'
): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .from('organization_memberships')
      .select('role')
      .eq('user_id', userId)
      .eq('organization_id', organizationId)
      .eq('status', 'active')
      .single();

    if (error || !data) {
      return false;
    }

    // Define role hierarchy (higher roles include lower role permissions)
    const roleHierarchy = {
      'owner': ['owner', 'admin', 'manager', 'member', 'viewer'],
      'admin': ['admin', 'manager', 'member', 'viewer'],
      'manager': ['manager', 'member', 'viewer'],
      'member': ['member', 'viewer'],
      'viewer': ['viewer']
    };

    const userRole = data.role as keyof typeof roleHierarchy;
    return roleHierarchy[userRole]?.includes(requiredRole) || false;
  } catch (err) {
    console.error('Error checking organization role:', err);
    return false;
  }
}

/**
 * Create a new user in the database (called after Supabase Auth user creation)
 * @param supabase Supabase client
 * @param authUserId Supabase Auth user ID
 * @param email User email
 * @param fullName User full name
 * @param role User role
 */
export async function createDatabaseUser(
  supabase: SupabaseClient<Database>,
  authUserId: string,
  email: string,
  fullName: string,
  role: 'platform_admin' | 'individual' | 'org_owner' | 'org_admin' | 'org_member' = 'individual'
) {
  try {
    const { data, error } = await supabase
      .from('users')
      .insert({
        id: authUserId,
        email,
        full_name: fullName,
        role,
        status: 'active'
      })
      .select()
      .single();

    if (error) throw error;

    // Create initial user context
    await updateUserContext(supabase, authUserId, 'individual');

    return { user: data, error: null };
  } catch (err) {
    console.error('Error creating database user:', err);
    return {
      user: null,
      error: err instanceof Error ? err : new Error('Unknown error creating user')
    };
  }
}

/**
 * Fetches a user's role from the database
 * @param supabase Supabase client
 * @param userId User ID to check
 * @returns User's role or null on error
 */
export async function getUserRole(
  supabase: SupabaseClient<Database>,
  userId: string
) {
  try {
    const { data: user, error } = await supabase
      .from('users')
      .select('role')
      .eq('id', userId)
      .single();

    if (error) {
      console.error('Error fetching user role:', error);
      return null;
    }

    return user?.role || null;
  } catch (err) {
    console.error('Error in getUserRole:', err);
    return null;
  }
}

/**
 * Check organization employment relationships
 * @param supabase Supabase client
 * @param userId User ID to check
 * @returns Employment relationship result
 */
export async function checkEmploymentRelationships(
  supabase: SupabaseClient<Database>,
  userId: string
) {
  try {
    // Get user's employment relationships
    const { data: employments, error } = await supabase
      .from('employment_relationships')
      .select(`
        organization_id,
        department_id,
        role,
        status,
        organizations!inner(name),
        departments!inner(name)
      `)
      .eq('user_id', userId)
      .eq('status', 'active');

    if (error) {
      return {
        hasEmployment: false,
        organizationId: null,
        error: error,
        policyError: null
      };
    }

    const hasEmployment = employments && employments.length > 0;
    const organizationId = hasEmployment ? employments[0].organization_id : null;

    return {
      hasEmployment,
      organizationId,
      employments: employments || [],
      error: null,
      policyError: null
    };
  } catch (err) {
    return {
      hasEmployment: false,
      organizationId: null,
      employments: [],
      error: err instanceof Error ? err : new Error('Unknown error'),
      policyError: null
    };
  }
}
