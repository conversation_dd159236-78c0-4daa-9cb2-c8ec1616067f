"use client"

import type React from "react"

import { useState, useEffect } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { useRouter } from "next/navigation"
import {
  Settings,
  LayoutDashboard,
  Users,
  User,
  Building2,
  BookOpen,
  LogOut,
  Sun,
  Moon,
  HelpCircle,
  Briefcase,
  Calendar,
  BarChart,
  Clock
} from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useTheme } from "next-themes"
import { supabase } from "@/lib/supabase"
import { ContextSwitcher } from "@/components/luna/context-switcher"

interface NavItem {
  title: string
  href: string
  icon: React.ElementType
}

interface NavSection {
  title?: string
  items: NavItem[]
}

const navSections: NavSection[] = [
  {
    title: "Pages & Inner Pages",
    items: [
      {
        title: "Dashboard",
        href: "/org/default",
        icon: LayoutDashboard,
      },
      {
        title: "Profile",
        href: "/org/default/profile",
        icon: User,
      },
      {
        title: "Departments",
        href: "/org/default/departments",
        icon: Users,
      },
    ]
  },
  {
    items: [
      {
        title: "Vacancies",
        href: "/org/default/vacancies",
        icon: Briefcase,
      },
      {
        title: "Applications",
        href: "/org/default/applications",
        icon: BookOpen,
      },
      {
        title: "Interviews",
        href: "/org/default/interviews",
        icon: Calendar,
      },
      {
        title: "My Schedule",
        href: "/org/default/schedule",
        icon: Clock,
      },
    ]
  },
  {
    items: [
      {
        title: "Analytics",
        href: "/organization/analytics",
        icon: BarChart,
      },
      {
        title: "Settings",
        href: "/organization/settings",
        icon: Settings,
      },
      {
        title: "Help & Support",
        href: "/organization/help",
        icon: HelpCircle,
      },
    ]
  }
]

export function OrganizationDashboardLayout({ children }: { children: React.ReactNode }) {
  const [mounted, setMounted] = useState(false)
  const { theme, setTheme } = useTheme()
  const pathname = usePathname()
  const router = useRouter()

  const handleSignOut = async () => {
    await supabase.auth.signOut()
    router.push('/login')
    router.refresh()
  }

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return null
  }

  return (
    <div className="flex h-screen">
      {/* Left Sidebar */}
      <div className="h-full bg-white dark:bg-gray-950 border-r border-gray-200 dark:border-gray-800 w-64">
        <div className="flex flex-col h-full">
          {/* Logo Header */}
          <div className="flex h-16 items-center border-b border-gray-200 dark:border-gray-800 px-4">
            <Link href="/organization" className="flex items-center">
              <img
                src="/Luna_Logo_Dark.PNG"
                alt="Luna Logo"
                className="h-12 w-auto"
              />
            </Link>
          </div>
          
          {/* Navigation Menu */}
          <div className="flex-grow overflow-y-auto py-4">
            {navSections.map((section, sectionIndex) => (
              <div key={sectionIndex} className="mb-6">
                {section.title && (
                  <div className="px-3 mb-2">
                    <p className="text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                      {section.title}
                    </p>
                  </div>
                )}
                <nav className="space-y-1">
                  {section.items.map((item) => (
                    <Link
                      key={item.href}
                      href={item.href}
                      className={cn(
                        "flex items-center gap-3 px-3 py-2 text-sm font-medium rounded-md",
                        pathname === item.href
                          ? "bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-600 dark:from-blue-950/50 dark:to-indigo-950/50 dark:text-blue-400"
                          : "text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800/60"
                      )}
                    >
                      <item.icon
                        className={cn(
                          "h-5 w-5",
                          pathname === item.href
                            ? "text-blue-600 dark:text-blue-400"
                            : "text-gray-500 dark:text-gray-400"
                        )}
                      />
                      <span>{item.title}</span>
                    </Link>
                  ))}
                </nav>
                {sectionIndex < navSections.length - 1 && (
                  <div className="mt-6 px-3">
                    <Separator className="bg-gray-200 dark:bg-gray-800" />
                  </div>
                )}
              </div>
            ))}
          </div>
          
          {/* Footer with user menu */}
          <div className="border-t border-gray-200 dark:border-gray-800 p-4">
            <div className="flex justify-between items-center">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon" className="rounded-full">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src="/avatars/org-admin.png" alt="Organization Admin" />
                      <AvatarFallback>OA</AvatarFallback>
                    </Avatar>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>Organization Account</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => setTheme(theme === "dark" ? "light" : "dark")}>
                    {theme === "dark" ? (
                      <Sun className="mr-2 h-4 w-4" />
                    ) : (
                      <Moon className="mr-2 h-4 w-4" />
                    )}
                    <span>{theme === "dark" ? "Light Mode" : "Dark Mode"}</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleSignOut}>
                    <LogOut className="mr-2 h-4 w-4" />
                    <span>Log Out</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col min-w-0">
        <header className="border-b border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-950">
          <div className="px-4 sm:px-6 lg:px-8 py-4 flex items-center justify-between">
            <h1 className="text-2xl font-bold tracking-tight text-gray-900 dark:text-gray-100">
              {pathname === '/organization'
                ? 'Organization Dashboard'
                : pathname === '/organization/profile'
                ? 'Organization Profile'
                : pathname === '/organization/team'
                ? 'Team Management'
                : pathname === '/organization/vacancies'
                ? 'Vacancies'
                : pathname === '/organization/applications'
                ? 'Applications'
                : pathname === '/organization/interviews'
                ? 'Interviews'
                : pathname === '/organization/schedule'
                ? 'My Schedule'
                : pathname === '/organization/analytics'
                ? 'Analytics'
                : pathname === '/organization/settings'
                ? 'Settings'
                : pathname === '/organization/help'
                ? 'Help & Support'
                : 'Organization Portal'}
            </h1>
            <div className="flex items-center space-x-4">
              <ContextSwitcher />
            </div>
          </div>
        </header>
        <main className="flex-1 overflow-auto bg-gray-50 dark:bg-gray-900">
          {children}
        </main>
      </div>
    </div>
  )
} 