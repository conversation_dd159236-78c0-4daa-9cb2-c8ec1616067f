import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { Database } from '@/types/supabase'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const supabase = createRouteHandlerClient<Database>({ cookies })

    // Check if user is authenticated
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Fetch module name
    const { data: module, error } = await supabase
      .from('course_modules')
      .select('name')
      .eq('id', id)
      .single()

    if (error || !module) {
      return NextResponse.json({ error: 'Module not found' }, { status: 404 })
    }

    return NextResponse.json({ moduleName: module.name })
  } catch (error) {
    console.error('Error fetching module breadcrumb:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
