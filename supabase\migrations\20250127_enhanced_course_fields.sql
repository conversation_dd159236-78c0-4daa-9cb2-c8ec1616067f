-- =============================================================================
-- ENHANCED COURSE FIELDS MIGRATION
-- Adds missing fields for basic/advanced course configurations
-- =============================================================================

-- =============================================================================
-- STEP 1: ADD MISSING COURSE FIELDS
-- =============================================================================

-- Add course complexity and additional business fields
ALTER TABLE courses 
ADD COLUMN course_complexity TEXT DEFAULT 'basic' CHECK (course_complexity IN ('basic', 'advanced')),
ADD COLUMN certification_available BOOLEAN DEFAULT false,
ADD COLUMN is_standalone BOOLEAN DEFAULT true,
ADD COLUMN required_software TEXT[] DEFAULT '{}',
ADD COLUMN hardware_requirements TEXT,
ADD COLUMN language TEXT DEFAULT 'en',
ADD COLUMN accessibility_features TEXT[] DEFAULT '{}';

-- Add course prerequisites reference (self-referencing)
ALTER TABLE courses 
ADD COLUMN prerequisite_courses UUID[] DEFAULT '{}';

-- =============================================================================
-- STEP 2: ADD MISSING MODULE FIELDS
-- =============================================================================

-- Add module type and complexity fields
ALTER TABLE course_modules 
ADD COLUMN module_type TEXT DEFAULT 'theory' CHECK (module_type IN ('theory', 'practical', 'assessment', 'project')),
ADD COLUMN difficulty_level TEXT DEFAULT 'beginner' CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced')),
ADD COLUMN required_resources TEXT[] DEFAULT '{}',
ADD COLUMN module_prerequisites UUID[] DEFAULT '{}';

-- =============================================================================
-- STEP 3: ADD MISSING LESSON FIELDS
-- =============================================================================

-- Add assessment and resource fields to lessons
ALTER TABLE course_lessons 
ADD COLUMN has_assessment BOOLEAN DEFAULT false,
ADD COLUMN assessment_type TEXT CHECK (assessment_type IN ('quiz', 'assignment', 'peer_review', 'self_assessment')),
ADD COLUMN weight_in_module DECIMAL(5,2) DEFAULT 0,
ADD COLUMN required_software TEXT[] DEFAULT '{}',
ADD COLUMN downloadable_resources JSONB DEFAULT '[]',
ADD COLUMN external_links JSONB DEFAULT '[]',
ADD COLUMN lesson_prerequisites UUID[] DEFAULT '{}';

-- Add content-specific fields for different lesson types
ALTER TABLE course_lessons 
ADD COLUMN video_duration INTEGER, -- for video lessons
ADD COLUMN transcript TEXT, -- for video lessons
ADD COLUMN reading_time INTEGER, -- for text lessons
ADD COLUMN passing_score DECIMAL(5,2), -- for quiz lessons
ADD COLUMN attempts_allowed INTEGER DEFAULT 3, -- for quiz lessons
ADD COLUMN time_limit INTEGER, -- for quiz/assignment lessons
ADD COLUMN submission_format TEXT[] DEFAULT '{}', -- for assignment lessons
ADD COLUMN max_file_size INTEGER; -- for assignment lessons (in MB)

-- =============================================================================
-- STEP 4: CREATE COURSE CONFIGURATION TEMPLATES
-- =============================================================================

-- Create a table to store course configuration templates
CREATE TABLE course_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  complexity TEXT NOT NULL CHECK (complexity IN ('basic', 'advanced')),
  default_fields JSONB DEFAULT '{}', -- Default field values for this template
  enabled_features TEXT[] DEFAULT '{}', -- Which features are enabled
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(name)
);

-- Insert default templates
INSERT INTO course_templates (name, description, complexity, default_fields, enabled_features) VALUES
(
  'Basic Course Template',
  'Simple course structure for soft skills, compliance, and basic training',
  'basic',
  '{"course_complexity": "basic", "certification_available": false, "has_prerequisites": false}',
  '{"basic_content", "simple_assessment", "progress_tracking"}'
),
(
  'Advanced Course Template',
  'Complex course structure for technical training and certifications',
  'advanced',
  '{"course_complexity": "advanced", "certification_available": true, "has_prerequisites": true}',
  '{"advanced_content", "complex_assessment", "prerequisites", "branching_logic", "detailed_analytics", "certification"}'
),
(
  'Compliance Training Template',
  'Streamlined template for mandatory compliance and safety training',
  'basic',
  '{"course_complexity": "basic", "certification_available": true, "is_mandatory": true}',
  '{"basic_content", "completion_tracking", "certification", "reporting"}'
),
(
  'Technical Certification Template',
  'Comprehensive template for technical skills and professional certifications',
  'advanced',
  '{"course_complexity": "advanced", "certification_available": true, "has_prerequisites": true, "has_projects": true}',
  '{"advanced_content", "hands_on_labs", "complex_assessment", "prerequisites", "certification", "industry_alignment"}'
);

-- =============================================================================
-- STEP 5: ADD INDEXES FOR NEW FIELDS
-- =============================================================================

-- Index for course complexity queries
CREATE INDEX idx_courses_complexity ON courses(course_complexity);
CREATE INDEX idx_courses_certification ON courses(certification_available) WHERE certification_available = true;
CREATE INDEX idx_courses_standalone ON courses(is_standalone) WHERE is_standalone = true;

-- Index for module type queries
CREATE INDEX idx_course_modules_type ON course_modules(module_type);
CREATE INDEX idx_course_modules_difficulty ON course_modules(difficulty_level);

-- Index for lesson assessment queries
CREATE INDEX idx_course_lessons_assessment ON course_lessons(has_assessment) WHERE has_assessment = true;
CREATE INDEX idx_course_lessons_type_assessment ON course_lessons(lesson_type, has_assessment);

-- =============================================================================
-- STEP 6: ADD UPDATED_AT TRIGGERS FOR NEW TABLES
-- =============================================================================

-- Add trigger for course_templates
CREATE TRIGGER update_course_templates_updated_at 
BEFORE UPDATE ON course_templates 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =============================================================================
-- STEP 7: ADD COMMENTS FOR DOCUMENTATION
-- =============================================================================

COMMENT ON COLUMN courses.course_complexity IS 'Determines UI complexity: basic (simple forms) or advanced (full feature set)';
COMMENT ON COLUMN courses.certification_available IS 'Whether course offers completion certificate';
COMMENT ON COLUMN courses.prerequisite_courses IS 'Array of course IDs that must be completed first';

COMMENT ON COLUMN course_modules.module_type IS 'Type of module: theory, practical, assessment, or project';
COMMENT ON COLUMN course_modules.required_resources IS 'Special tools, software, or materials needed';

COMMENT ON COLUMN course_lessons.has_assessment IS 'Whether lesson includes graded evaluation';
COMMENT ON COLUMN course_lessons.weight_in_module IS 'Percentage contribution to module grade (0-100)';
COMMENT ON COLUMN course_lessons.downloadable_resources IS 'JSON array of downloadable files and materials';

COMMENT ON TABLE course_templates IS 'Predefined course configurations for basic vs advanced course creation';

-- =============================================================================
-- MIGRATION COMPLETE
-- =============================================================================

SELECT 'Enhanced course fields migration completed successfully!' as message;
SELECT 'Added support for basic/advanced course configurations with templates' as details;
