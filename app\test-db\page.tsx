'use client';

import { useState, useEffect } from 'react';
import { createBrowserClient } from '@/lib/supabase';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function TestDbPage() {
  const [results, setResults] = useState<any>({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    testTables();
  }, []);

  const testTables = async () => {
    const supabase = createBrowserClient();
    const testResults: any = {};

    try {
      // Test skill_categories table
      console.log('Testing skill_categories table...');
      const { data: categories, error: catError } = await supabase
        .from('skill_categories')
        .select('*')
        .limit(5);
      
      testResults.skill_categories = {
        exists: !catError,
        error: catError?.message,
        count: categories?.length || 0,
        data: categories
      };

      // Test skills table
      console.log('Testing skills table...');
      const { data: skills, error: skillsError } = await supabase
        .from('skills')
        .select('*')
        .limit(5);
      
      testResults.skills = {
        exists: !skillsError,
        error: skillsError?.message,
        count: skills?.length || 0,
        data: skills
      };

      // Test job_roles table
      console.log('Testing job_roles table...');
      const { data: jobRoles, error: jobRolesError } = await supabase
        .from('job_roles')
        .select('*')
        .limit(5);
      
      testResults.job_roles = {
        exists: !jobRolesError,
        error: jobRolesError?.message,
        count: jobRoles?.length || 0,
        data: jobRoles
      };

      console.log('Test results:', testResults);
      setResults(testResults);

    } catch (error) {
      console.error('Test error:', error);
      testResults.error = error;
      setResults(testResults);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <div className="p-6">Testing database tables...</div>;
  }

  return (
    <div className="p-6 space-y-6">
      <h1 className="text-2xl font-bold">Database Tables Test</h1>
      
      {Object.entries(results).map(([tableName, result]: [string, any]) => (
        <Card key={tableName}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {tableName}
              {result.exists ? (
                <span className="text-green-600 text-sm">✓ EXISTS</span>
              ) : (
                <span className="text-red-600 text-sm">✗ MISSING</span>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div>Count: {result.count}</div>
              {result.error && (
                <div className="text-red-600 text-sm">Error: {result.error}</div>
              )}
              {result.data && result.data.length > 0 && (
                <details>
                  <summary className="cursor-pointer text-sm text-blue-600">
                    Show sample data
                  </summary>
                  <pre className="text-xs bg-gray-100 p-2 rounded mt-2 overflow-auto">
                    {JSON.stringify(result.data, null, 2)}
                  </pre>
                </details>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
