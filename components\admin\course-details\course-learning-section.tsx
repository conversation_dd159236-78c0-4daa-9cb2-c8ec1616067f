"use client"

import React, { useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Save, Target, Users, BookOpen } from 'lucide-react'
import { toast } from "sonner"

interface Course {
  id: string
  name: string
  description: string
  slug: string
  level: string
  estimated_duration: number
  status: string
  price: number
  cover_image_url?: string
  preview_video_url?: string
  meta_description?: string
  tags: string[]
  learning_objectives: string[]
  target_audience?: string
  instructor_id?: string
  instructor_bio?: string
  enrollment_count: number
  completion_rate: number
  average_rating: number
  course_complexity: string
  certification_available: boolean
  is_standalone: boolean
  required_software: string[]
  hardware_requirements?: string
  language: string
  accessibility_features: string[]
  prerequisite_courses: string[]
  created_at: string
  updated_at: string
}

interface CourseLearningDetailsProps {
  course: Course
  onUpdate: () => void
}

export function CourseLearningSection({ course, onUpdate }: CourseLearningDetailsProps) {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    learning_objectives: course.learning_objectives.join('\n'),
    target_audience: course.target_audience || '',
    instructor_bio: course.instructor_bio || ''
  })

  const handleSave = async () => {
    try {
      setLoading(true)

      const payload = {
        learning_objectives: formData.learning_objectives 
          ? formData.learning_objectives.split('\n').map(obj => obj.trim()).filter(obj => obj.length > 0)
          : [],
        target_audience: formData.target_audience?.trim() || null,
        instructor_bio: formData.instructor_bio?.trim() || null
      }

      const response = await fetch(`/api/admin/courses/${course.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to update learning details')
      }

      toast.success('Learning details updated successfully')
      onUpdate()
      
    } catch (error: any) {
      console.error('Error updating learning details:', error)
      toast.error(error.message || 'Failed to update learning details')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-end">
        <Button
          size="sm"
          onClick={handleSave}
          disabled={loading}
        >
          <Save className="h-4 w-4 mr-2" />
          {loading ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>
        <div className="space-y-4">
          {/* Learning Objectives */}
          <div className="space-y-2">
            <Label htmlFor="learning_objectives" className="flex items-center gap-2">
              <Target className="h-4 w-4" />
              Learning Objectives
            </Label>
            <Textarea
              id="learning_objectives"
              value={formData.learning_objectives}
              onChange={(e) => setFormData({ ...formData, learning_objectives: e.target.value })}
              placeholder="Enter each learning objective on a new line:&#10;• Understand the fundamentals of...&#10;• Be able to implement...&#10;• Master the techniques for..."
              rows={6}
            />
            <p className="text-sm text-muted-foreground">
              Enter each learning objective on a separate line. These will be displayed as bullet points to students.
            </p>
          </div>

          {/* Target Audience */}
          <div className="space-y-2">
            <Label htmlFor="target_audience" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Target Audience
            </Label>
            <Textarea
              id="target_audience"
              value={formData.target_audience}
              onChange={(e) => setFormData({ ...formData, target_audience: e.target.value })}
              placeholder="Describe who this course is designed for:&#10;• Beginners with no prior experience&#10;• Professionals looking to upskill&#10;• Students preparing for certification..."
              rows={4}
            />
            <p className="text-sm text-muted-foreground">
              Help potential students understand if this course is right for them.
            </p>
          </div>

          {/* Instructor Bio */}
          <div className="space-y-2">
            <Label htmlFor="instructor_bio" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Instructor Bio
            </Label>
            <Textarea
              id="instructor_bio"
              value={formData.instructor_bio}
              onChange={(e) => setFormData({ ...formData, instructor_bio: e.target.value })}
              placeholder="Brief biography of the course instructor, including relevant experience, qualifications, and expertise..."
              rows={4}
            />
            <p className="text-sm text-muted-foreground">
              Share the instructor's background and credentials to build trust with students.
            </p>
          </div>
        </div>
    </div>
  )
}
