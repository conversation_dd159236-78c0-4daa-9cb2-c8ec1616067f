"use client"

import type React from "react"

import { useState, useEffect } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { useRouter } from "next/navigation"
import {
  BookOpen,
  Brain,
  Calendar,
  ClipboardCheck,
  FileCheck,
  Home,
  Settings,
  User,
  Briefcase,
  HelpCircle,
  Sun,
  Moon,
  GraduationCap,
  LogOut,
  Bell,
  UserCog,
  Phone,
  FileText
} from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { UserAvatar } from "@/components/ui/user-avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useTheme } from "next-themes"
import { supabase } from "@/lib/supabase"
import { ContextSwitcher } from "@/components/luna/context-switcher"

interface NavItem {
  title: string
  href: string
  icon: React.ElementType
  badge?: number
}

interface NavSection {
  title: string
  items: NavItem[]
}

const navSections: NavSection[] = [
  {
    title: "Learning",
    items: [
      {
        title: "Dashboard",
        href: "/individual",
        icon: Home,
      },
      {
        title: "Profile",
        href: "/individual/profile",
        icon: User,
      },
      {
        title: "Training",
        href: "/individual/training",
        icon: BookOpen,
        badge: 0,
      },
      {
        title: "Role-Call Training",
        href: "/individual/role-call-training",
        icon: Phone,
      },
      {
        title: "Assessments",
        href: "/individual/assessments",
        icon: ClipboardCheck,
        badge: 0,
      },
      {
        title: "AI Assessments",
        href: "/individual/ai-assessments",
        icon: Brain,
        badge: 0,
      },
      {
        title: "Files",
        href: "/individual/files",
        icon: FileCheck,
      },
    ],
  },
  {
    title: "Career",
    items: [
      {
        title: "Job Board",
        href: "/individual/job-board",
        icon: Briefcase,
        badge: 0,
      },
      {
        title: "My Applications",
        href: "/individual/applications",
        icon: ClipboardCheck,
        badge: 0,
      },
      {
        title: "Interviews",
        href: "/individual/interviews",
        icon: Calendar,
        badge: 0,
      },
    ],
  },
  {
    title: "Support",
    items: [
      {
        title: "Settings",
        href: "/individual/settings",
        icon: Settings,
      },
      {
        title: "Help & Support",
        href: "/individual/help",
        icon: HelpCircle,
      },
    ],
  },
]

export function DashboardLayout({ children }: { children: React.ReactNode }) {
  const [mounted, setMounted] = useState(false)
  const [userData, setUserData] = useState<{full_name: string; email: string; avatar_url?: string | null} | null>(null)
  const { theme, setTheme } = useTheme()
  const pathname = usePathname()
  const router = useRouter()
  const [menuCounts, setMenuCounts] = useState({
    training: 0,
    assessments: 0,
    jobs: 0,
    interviews: 0
  })
  const [navItems, setNavItems] = useState(navSections)

  const handleSignOut = async () => {
    await supabase.auth.signOut()
    router.push('/login')
    router.refresh()
  }

  useEffect(() => {
    setMounted(true)

    // Fetch user data
    const fetchUserData = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser()

        if (user) {
          const { data, error } = await supabase
            .from('users')
            .select('full_name, email, avatar_url')
            .eq('id', user.id)
            .single()

          if (data && !error) {
            setUserData(data)
          }
        }
      } catch (error) {
        console.error('Error fetching user data:', error)
      }
    }

    // Fetch counts for menu badges
    const fetchMenuCounts = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser()

        if (user) {
          // Get count of training modules
          const { count: trainingCount, error: trainingError } = await supabase
            .from('training_modules')
            .select('*', { count: 'exact', head: true })
            .eq('status', 'published')

          // Get count of uncompleted assessments for this user
          const { data: completedAssessments } = await supabase
            .from('assessment_completions')
            .select('assessment_id')
            .eq('user_id', user.id)
            .eq('status', 'completed')

          const completedAssessmentIds = completedAssessments?.map(c => c.assessment_id) || []

          let assessmentsCount = 0
          if (completedAssessmentIds.length === 0) {
            // If no completed assessments, count all active assessments
            const { count } = await supabase
              .from('assessments')
              .select('*', { count: 'exact', head: true })
              .eq('is_active', true)
            assessmentsCount = count || 0
          } else {
            // Count assessments not in completed list
            const { count } = await supabase
              .from('assessments')
              .select('*', { count: 'exact', head: true })
              .eq('is_active', true)
              .not('id', 'in', `(${completedAssessmentIds.join(',')})`)
            assessmentsCount = count || 0
          }

          // Get count of job postings
          const { count: jobsCount, error: jobsError } = await supabase
            .from('job_postings')
            .select('*', { count: 'exact', head: true })
            .eq('status', 'open')

          // Get count of interviews
          const { count: interviewsCount, error: interviewsError } = await supabase
            .from('interviews')
            .select('*', { count: 'exact', head: true })
            .eq('status', 'scheduled')

          // Update the navigation sections with the counts
          const updatedSections = navSections.map(section => {
            const updatedItems = section.items.map(item => {
              if (item.title === 'Training') {
                return { ...item, badge: trainingCount || 0 }
              } else if (item.title === 'Assessments') {
                return { ...item, badge: assessmentsCount || 0 }
              } else if (item.title === 'Job Board') {
                return { ...item, badge: jobsCount || 0 }
              } else if (item.title === 'Interviews') {
                return { ...item, badge: interviewsCount || 0 }
              }
              return item
            })
            return { ...section, items: updatedItems }
          })

          setNavItems(updatedSections)
        }
      } catch (error) {
        console.error('Error fetching menu counts:', error)
      }
    }

    fetchUserData()
    fetchMenuCounts()
  }, [])

  if (!mounted) {
    return null
  }



  return (
    <div className="flex h-screen">
      {/* Left Sidebar */}
      <div className="h-full bg-white dark:bg-gray-950 border-r border-gray-200 dark:border-gray-800 w-64">
        <div className="flex flex-col h-full">
          {/* Logo Header */}
          <div className="flex h-16 items-center border-b border-gray-200 dark:border-gray-800 px-4">
            <Link href="/individual" className="flex items-center">
              <img
                src="/Luna_Logo_Dark.PNG"
                alt="Luna Logo"
                className="h-12 w-auto"
              />
            </Link>
          </div>

          {/* Navigation Menu */}
          <div className="flex-grow overflow-y-auto py-4">
            {navItems.map((section, sectionIndex) => (
              <div key={sectionIndex} className="mb-6">
                <div className="px-3 mb-2">
                  <p className="text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                    {section.title}
                  </p>
                </div>
                <nav className="space-y-1">
                  {section.items.map((item) => (
                    <Link
                      key={item.href}
                      href={item.href}
                      className={cn(
                        "flex items-center justify-between px-3 py-2 text-sm font-medium rounded-md",
                        pathname === item.href
                          ? "bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-600 dark:from-blue-950/50 dark:to-indigo-950/50 dark:text-blue-400"
                          : "text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800/60"
                      )}
                    >
                      <div className="flex items-center gap-3">
                        <item.icon
                          className={cn(
                            "h-5 w-5",
                            pathname === item.href
                              ? "text-blue-600 dark:text-blue-400"
                              : "text-gray-500 dark:text-gray-400"
                          )}
                        />
                        <span>{item.title}</span>
                      </div>
                      {item.badge !== undefined && (
                        <div
                          className={cn(
                            "ml-auto rounded-full px-2 py-0.5 text-xs font-medium",
                            pathname === item.href
                              ? "bg-blue-100 text-blue-600 dark:bg-blue-900/60 dark:text-blue-300"
                              : "bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-300"
                          )}
                        >
                          {item.badge}
                        </div>
                      )}
                    </Link>
                  ))}
                </nav>
                {sectionIndex < navItems.length - 1 && (
                  <div className="mt-6 px-3">
                    <Separator className="bg-gray-200 dark:bg-gray-800" />
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Footer with user menu */}
          <div className="border-t border-gray-200 dark:border-gray-800 p-4">
            <div className="flex justify-between items-center">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="flex items-center gap-2 px-2 rounded-full w-full justify-start">
                    <UserAvatar
                      user={{
                        full_name: userData?.full_name,
                        email: userData?.email,
                        avatar_url: userData?.avatar_url
                      }}
                      size="sm"
                    />
                    <div className="flex flex-col items-start text-sm">
                      <span className="font-medium truncate w-28">
                        {userData?.full_name || 'User'}
                      </span>
                    </div>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>User Account</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => router.push('/prospect/account')}>
                    <UserCog className="mr-2 h-4 w-4" />
                    <span>Account Settings</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setTheme(theme === "dark" ? "light" : "dark")}>
                    {theme === "dark" ? (
                      <Sun className="mr-2 h-4 w-4" />
                    ) : (
                      <Moon className="mr-2 h-4 w-4" />
                    )}
                    <span>{theme === "dark" ? "Light Mode" : "Dark Mode"}</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleSignOut}>
                    <LogOut className="mr-2 h-4 w-4" />
                    <span>Log Out</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col min-w-0">
        <header className="border-b border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-950">
          <div className="px-4 sm:px-6 lg:px-8 py-4 flex items-center justify-between">
            <h1 className="text-2xl font-bold tracking-tight text-gray-900 dark:text-gray-100">
              {pathname === '/individual'
                ? 'Individual Dashboard'
                : pathname === '/individual/profile'
                ? 'Profile'
                : pathname === '/individual/training'
                ? 'Training'
                : pathname === '/individual/role-call-training'
                ? 'Role-Call Training'
                : pathname === '/individual/role-call-training/cold-call'
                ? 'Cold Call Training'
                : pathname.startsWith('/individual/role-call-training/')
                ? 'Role-Call Training'
                : pathname === '/individual/assessments'
                ? 'Assessments'
                : pathname === '/individual/ai-assessments'
                ? 'AI Assessments'
                : pathname.startsWith('/individual/ai-assessments/')
                ? 'AI Assessments'
                : pathname === '/individual/certificates'
                ? 'Certificates'
                : pathname === '/individual/job-board'
                ? 'Job Board'
                : pathname === '/individual/applications'
                ? 'My Applications'
                : pathname === '/individual/interviews'
                ? 'Interviews'
                : pathname === '/individual/settings'
                ? 'Platform Settings'
                : pathname === '/individual/account'
                ? 'Account Settings'
                : pathname === '/individual/help'
                ? 'Help & Support'
                : 'Luna Skills Platform'}
            </h1>
            <div className="flex items-center space-x-2">
              <ContextSwitcher />
              <Button variant="ghost" size="icon" className="relative">
                <Bell className="h-5 w-5" />
                <span className="absolute top-1 right-1 h-2 w-2 rounded-full bg-red-500" />
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon" className="rounded-full">
                    <UserAvatar
                      user={{
                        full_name: userData?.full_name,
                        email: userData?.email,
                        avatar_url: userData?.avatar_url
                      }}
                      size="sm"
                    />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>User Account</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => router.push('/prospect/account')}>
                    <UserCog className="mr-2 h-4 w-4" />
                    <span>Account Settings</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setTheme(theme === "dark" ? "light" : "dark")}>
                    {theme === "dark" ? (
                      <Sun className="mr-2 h-4 w-4" />
                    ) : (
                      <Moon className="mr-2 h-4 w-4" />
                    )}
                    <span>{theme === "dark" ? "Light Mode" : "Dark Mode"}</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleSignOut}>
                    <LogOut className="mr-2 h-4 w-4" />
                    <span>Log Out</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </header>
        <main className="flex-1 overflow-auto bg-gray-50 dark:bg-gray-900">
          {children}
        </main>
      </div>
    </div>
  )
}
