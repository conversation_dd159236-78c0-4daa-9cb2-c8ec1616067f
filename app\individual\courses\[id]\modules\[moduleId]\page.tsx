import { createServerComponentClient } from "@supabase/auth-helpers-nextjs"
import { createClient } from '@supabase/supabase-js'
import { cookies } from "next/headers"
import { redirect } from "next/navigation"
import { Database } from "@/types/supabase"
import ModuleDetailsPage from "@/components/module-details-page"

// Create admin client for operations that need to bypass RLS
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || ""
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || ""
const adminClient = createClient<Database>(supabaseUrl, supabaseServiceKey)

export default async function ModuleDetailsPageRoute({
  params,
}: {
  params: { id: string; moduleId: string }
}) {
  const { id: courseId, moduleId } = params
  const supabase = createServerComponentClient<Database>({ cookies })
  
  // Check if user is authenticated
  const { data: { session } } = await supabase.auth.getSession()
  if (!session) {
    redirect("/login")
  }
  
  // Get the prospect's profile with admin client to bypass RLS
  let prospectData = null

  try {
    const { data } = await adminClient
      .from("prospects")
      .select("id, training_status")
      .eq("user_id", session.user.id)

    if (data && data.length > 0) {
      prospectData = data[0]
    }
  } catch (error) {
    console.error("Error fetching prospect data:", error)
  }

  const prospectId = prospectData?.id
  
  // Fetch the course using admin client
  const { data: course, error: courseError } = await adminClient
    .from("courses")
    .select("*")
    .eq("id", courseId)
    .single()
  
  if (courseError || !course) {
    redirect("/individual/courses-marketplace")
  }
  
  // Fetch the module using admin client
  const { data: module, error: moduleError } = await adminClient
    .from("course_modules")
    .select("*")
    .eq("id", moduleId)
    .eq("course_id", courseId)
    .single()

  if (moduleError || !module) {
    redirect(`/individual/courses/${courseId}`)
  }
  
  // Fetch the lessons using admin client
  const { data: lessons, error: lessonsError } = await adminClient
    .from("course_lessons")
    .select("*")
    .eq("module_id", moduleId)
    .order("sequence_order", { ascending: true })
  
  if (lessonsError) {
    console.error("Error fetching lessons:", lessonsError)
  }
  
  // For now, we'll focus on regular lessons (no quizzes in the new course system yet)
  // Convert lessons to the expected format
  const allLessons = (lessons || []).map(lesson => ({
    id: lesson.id,
    name: lesson.name,
    description: lesson.description,
    lessonType: lesson.lesson_type || 'video',
    sequenceOrder: lesson.sequence_order,
    estimatedDuration: lesson.estimated_duration,
    isMandatory: lesson.is_mandatory || false,
    status: 'not_started' // TODO: Get actual status from progress
  }));
  
  // Check if user is enrolled in the course
  const { data: enrollment } = await adminClient
    .from("user_course_enrollments")
    .select("*")
    .eq("course_id", courseId)
    .eq("user_id", session.user.id)
    .single()
  
  const isEnrolled = !!enrollment
  
  // Format module data for the component
  const moduleData = {
    id: module.id,
    name: module.name,
    description: module.description,
    estimatedDuration: module.estimated_duration,
    price: module.single_price || 0,
    lessons: allLessons
  }
  
  return (
    <ModuleDetailsPage
      module={moduleData}
      courseId={courseId}
      courseName={course.name}
      isEnrolled={isEnrolled}
      enrollment={enrollment}
    />
  )
}
