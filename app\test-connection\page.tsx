'use client';

import { useEffect, useState } from 'react';

interface TestResult {
  success: boolean;
  message?: string;
  environment?: any;
  error?: string;
  details?: string;
  timestamp?: string;
}

export default function TestConnectionPage() {
  const [testResult, setTestResult] = useState<TestResult | null>(null);
  const [loading, setLoading] = useState(true);
  const [authTest, setAuthTest] = useState<any>(null);

  useEffect(() => {
    const runTests = async () => {
      try {
        // Test basic API connection
        console.log('Testing basic API connection...');
        const response = await fetch('/api/test-connection');
        const result = await response.json();
        setTestResult(result);

        // Test auth API
        console.log('Testing auth API...');
        const authResponse = await fetch('/api/auth/user');
        const authResult = await authResponse.json();
        setAuthTest(authResult);

      } catch (error) {
        console.error('Test failed:', error);
        setTestResult({
          success: false,
          error: 'Failed to connect to API',
          details: error instanceof Error ? error.message : 'Unknown error'
        });
      } finally {
        setLoading(false);
      }
    };

    runTests();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Running connection tests...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Connection Test Results</h1>
        
        {/* Basic API Test */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Basic API Connection Test</h2>
          {testResult ? (
            <div className={`p-4 rounded ${testResult.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
              <div className={`font-medium ${testResult.success ? 'text-green-800' : 'text-red-800'}`}>
                {testResult.success ? '✅ Success' : '❌ Failed'}
              </div>
              <div className="mt-2 text-sm text-gray-600">
                <p><strong>Message:</strong> {testResult.message || testResult.error}</p>
                {testResult.details && <p><strong>Details:</strong> {testResult.details}</p>}
                {testResult.environment && (
                  <div className="mt-2">
                    <strong>Environment:</strong>
                    <pre className="mt-1 text-xs bg-gray-100 p-2 rounded overflow-auto">
                      {JSON.stringify(testResult.environment, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="text-gray-500">No test result available</div>
          )}
        </div>

        {/* Auth API Test */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Auth API Test</h2>
          {authTest ? (
            <div className={`p-4 rounded ${authTest.success ? 'bg-green-50 border border-green-200' : 'bg-yellow-50 border border-yellow-200'}`}>
              <div className={`font-medium ${authTest.success ? 'text-green-800' : 'text-yellow-800'}`}>
                {authTest.success ? '✅ Auth Success' : '⚠️ No Authentication'}
              </div>
              <div className="mt-2 text-sm text-gray-600">
                <p><strong>Status:</strong> {authTest.status}</p>
                <p><strong>Error:</strong> {authTest.error || 'None'}</p>
                {authTest.user && (
                  <div className="mt-2">
                    <strong>User Info:</strong>
                    <pre className="mt-1 text-xs bg-gray-100 p-2 rounded overflow-auto">
                      {JSON.stringify(authTest.user, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="text-gray-500">No auth test result available</div>
          )}
        </div>

        <div className="mt-8 text-center">
          <button 
            onClick={() => window.location.reload()} 
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Run Tests Again
          </button>
        </div>
      </div>
    </div>
  );
}
