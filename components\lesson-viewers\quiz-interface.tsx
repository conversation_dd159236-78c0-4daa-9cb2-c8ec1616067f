"use client"

import React, { useState, useEffect, use<PERSON><PERSON>back } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Checkbox } from "@/components/ui/checkbox"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Progress } from "@/components/ui/progress"
import { Clock, CheckCircle, XCircle, AlertCircle, ChevronLeft, ChevronRight } from 'lucide-react'
import { toast } from "sonner"

interface QuizQuestion {
  id: string
  question_number: number
  question_text: string
  question_type: 'multiple_choice' | 'true_false' | 'short_answer' | 'essay' | 'code_challenge'
  question_context?: string
  answer_options?: Array<{
    id: string
    text: string
    is_correct: boolean
  }>
  correct_answer: string
  explanation?: string
  points: number
  estimated_time: number
}

interface QuizConfig {
  title: string
  instructions?: string
  time_limit?: number // in minutes
  passing_score: number
  attempts_allowed: number
  questions: QuizQuestion[]
}

interface QuizInterfaceProps {
  config: QuizConfig
  onSubmit: (answers: Record<string, any>, timeSpent: number) => void
  onProgress?: (progress: { currentQuestion: number; totalQuestions: number; answered: number }) => void
  autoSave?: boolean
}

export function QuizInterface({ config, onSubmit, onProgress, autoSave = true }: QuizInterfaceProps) {
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [answers, setAnswers] = useState<Record<string, any>>({})
  const [timeRemaining, setTimeRemaining] = useState<number>(config.time_limit ? config.time_limit * 60 : 0)
  const [timeSpent, setTimeSpent] = useState<number>(0)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [showResults, setShowResults] = useState(false)
  const [score, setScore] = useState<number>(0)

  const totalQuestions = config.questions.length
  const answeredCount = Object.keys(answers).length
  const progress = (currentQuestion + 1) / totalQuestions * 100

  // Timer effect
  useEffect(() => {
    if (config.time_limit && timeRemaining > 0 && !isSubmitted) {
      const timer = setInterval(() => {
        setTimeRemaining(prev => {
          if (prev <= 1) {
            handleSubmit()
            return 0
          }
          return prev - 1
        })
        setTimeSpent(prev => prev + 1)
      }, 1000)

      return () => clearInterval(timer)
    }
  }, [timeRemaining, isSubmitted, config.time_limit])

  // Progress tracking
  useEffect(() => {
    onProgress?.({
      currentQuestion: currentQuestion + 1,
      totalQuestions,
      answered: answeredCount
    })
  }, [currentQuestion, answeredCount, totalQuestions, onProgress])

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  const handleAnswerChange = useCallback((questionId: string, answer: any) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }))
  }, [])

  const goToQuestion = (index: number) => {
    if (index >= 0 && index < totalQuestions) {
      setCurrentQuestion(index)
    }
  }

  const goToPrevious = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(currentQuestion - 1)
    }
  }

  const goToNext = () => {
    if (currentQuestion < totalQuestions - 1) {
      setCurrentQuestion(currentQuestion + 1)
    }
  }

  const calculateScore = () => {
    let totalPoints = 0
    let earnedPoints = 0

    config.questions.forEach(question => {
      totalPoints += question.points
      const userAnswer = answers[question.id]
      
      if (userAnswer) {
        if (question.question_type === 'multiple_choice') {
          const correctOption = question.answer_options?.find(opt => opt.is_correct)
          if (correctOption && userAnswer === correctOption.id) {
            earnedPoints += question.points
          }
        } else if (question.question_type === 'true_false') {
          if (userAnswer === question.correct_answer) {
            earnedPoints += question.points
          }
        } else if (question.question_type === 'short_answer') {
          // Simple string comparison (in real app, this would be more sophisticated)
          if (userAnswer.toLowerCase().trim() === question.correct_answer.toLowerCase().trim()) {
            earnedPoints += question.points
          }
        }
      }
    })

    return Math.round((earnedPoints / totalPoints) * 100)
  }

  const handleSubmit = () => {
    const finalScore = calculateScore()
    setScore(finalScore)
    setIsSubmitted(true)
    setShowResults(true)
    onSubmit(answers, timeSpent)
    
    if (finalScore >= config.passing_score) {
      toast.success(`Quiz completed! Score: ${finalScore}%`)
    } else {
      toast.error(`Quiz completed. Score: ${finalScore}% (Passing: ${config.passing_score}%)`)
    }
  }

  const currentQuestionData = config.questions[currentQuestion]

  if (showResults) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {score >= config.passing_score ? (
              <CheckCircle className="h-6 w-6 text-green-500" />
            ) : (
              <XCircle className="h-6 w-6 text-red-500" />
            )}
            Quiz Results
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="text-center">
            <div className="text-4xl font-bold mb-2">
              {score}%
            </div>
            <div className="text-lg text-muted-foreground">
              {score >= config.passing_score ? 'Passed' : 'Failed'} 
              (Passing score: {config.passing_score}%)
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4 text-center">
            <div>
              <div className="text-2xl font-semibold">{answeredCount}</div>
              <div className="text-sm text-muted-foreground">Questions Answered</div>
            </div>
            <div>
              <div className="text-2xl font-semibold">{formatTime(timeSpent)}</div>
              <div className="text-sm text-muted-foreground">Time Spent</div>
            </div>
          </div>
          
          <Progress value={score} className="h-3" />
          
          {score < config.passing_score && config.attempts_allowed > 1 && (
            <div className="text-center">
              <Button onClick={() => window.location.reload()}>
                Retake Quiz
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      {/* Quiz Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>{config.title}</CardTitle>
            {config.time_limit && (
              <div className="flex items-center gap-2 text-sm">
                <Clock className="h-4 w-4" />
                <span className={timeRemaining < 300 ? 'text-red-500 font-semibold' : ''}>
                  {formatTime(timeRemaining)}
                </span>
              </div>
            )}
          </div>
          {config.instructions && (
            <p className="text-sm text-muted-foreground">{config.instructions}</p>
          )}
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between text-sm text-muted-foreground mb-2">
            <span>Question {currentQuestion + 1} of {totalQuestions}</span>
            <span>{answeredCount} answered</span>
          </div>
          <Progress value={progress} className="h-2" />
        </CardContent>
      </Card>

      {/* Current Question */}
      <Card>
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="flex items-start gap-3">
              <div className="bg-primary text-primary-foreground rounded-full w-8 h-8 flex items-center justify-center text-sm font-semibold">
                {currentQuestion + 1}
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-semibold mb-2">
                  {currentQuestionData.question_text}
                </h3>
                {currentQuestionData.question_context && (
                  <div className="bg-muted p-3 rounded-lg mb-4">
                    <pre className="text-sm whitespace-pre-wrap">
                      {currentQuestionData.question_context}
                    </pre>
                  </div>
                )}
                
                {/* Answer Options */}
                <div className="space-y-3">
                  {currentQuestionData.question_type === 'multiple_choice' && (
                    <RadioGroup
                      value={answers[currentQuestionData.id] || ''}
                      onValueChange={(value) => handleAnswerChange(currentQuestionData.id, value)}
                    >
                      {currentQuestionData.answer_options?.map((option) => (
                        <div key={option.id} className="flex items-center space-x-2">
                          <RadioGroupItem value={option.id} id={option.id} />
                          <Label htmlFor={option.id} className="flex-1 cursor-pointer">
                            {option.text}
                          </Label>
                        </div>
                      ))}
                    </RadioGroup>
                  )}
                  
                  {currentQuestionData.question_type === 'true_false' && (
                    <RadioGroup
                      value={answers[currentQuestionData.id] || ''}
                      onValueChange={(value) => handleAnswerChange(currentQuestionData.id, value)}
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="true" id="true" />
                        <Label htmlFor="true" className="cursor-pointer">True</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="false" id="false" />
                        <Label htmlFor="false" className="cursor-pointer">False</Label>
                      </div>
                    </RadioGroup>
                  )}
                  
                  {currentQuestionData.question_type === 'short_answer' && (
                    <Input
                      value={answers[currentQuestionData.id] || ''}
                      onChange={(e) => handleAnswerChange(currentQuestionData.id, e.target.value)}
                      placeholder="Enter your answer..."
                    />
                  )}
                  
                  {currentQuestionData.question_type === 'essay' && (
                    <Textarea
                      value={answers[currentQuestionData.id] || ''}
                      onChange={(e) => handleAnswerChange(currentQuestionData.id, e.target.value)}
                      placeholder="Enter your detailed answer..."
                      rows={6}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Navigation */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <Button
              variant="outline"
              onClick={goToPrevious}
              disabled={currentQuestion === 0}
            >
              <ChevronLeft className="h-4 w-4 mr-2" />
              Previous
            </Button>
            
            <div className="flex items-center gap-2">
              {config.questions.map((_, index) => (
                <Button
                  key={index}
                  variant={index === currentQuestion ? "default" : answers[config.questions[index].id] ? "secondary" : "outline"}
                  size="sm"
                  onClick={() => goToQuestion(index)}
                  className="w-8 h-8 p-0"
                >
                  {index + 1}
                </Button>
              ))}
            </div>
            
            {currentQuestion === totalQuestions - 1 ? (
              <Button onClick={handleSubmit} disabled={answeredCount === 0}>
                Submit Quiz
              </Button>
            ) : (
              <Button onClick={goToNext}>
                Next
                <ChevronRight className="h-4 w-4 ml-2" />
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
